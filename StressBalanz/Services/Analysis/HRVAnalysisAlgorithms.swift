import Foundation

/**
 HRVAnalysisAlgorithms
 
 心率变异性分析算法集合
 包含时域和频域分析方法
 */
extension StressAnalysisEngine {
    
    // MARK: - HRV Metrics Structure
    
    struct HRVMetrics {
        // 时域指标
        let rmssd: Double      // RMSSD - 连续R-R间期差值的均方根
        let sdnn: Double       // SDNN - 所有R-R间期的标准差
        let pnn50: Double      // pNN50 - 相邻R-R间期差值>50ms的百分比
        let triangularIndex: Double // 三角指数
        
        // 统计指标
        let mean: Double       // 平均R-R间期
        let median: Double     // 中位数
        let range: Double      // 极差
        let cv: Double         // 变异系数
        
        // 几何指标
        let hrvTI: Double      // HRV三角指数
        let tinn: Double       // TINN - 三角差值直方图基底宽度
        
        var isValid: Bool {
            return rmssd > 0 && sdnn > 0 && !rmssd.isNaN && !sdnn.isNaN
        }
    }
    
    struct HeartRateMetrics {
        let average: Double
        let minimum: Double
        let maximum: Double
        let standardDeviation: Double
        let range: Double
        let percentile25: Double
        let percentile75: Double
        let skewness: Double    // 偏斜度
        let kurtosis: Double    // 峰度
        
        var isValid: Bool {
            return average > 0 && !average.isNaN && standardDeviation >= 0
        }
    }
    
    // MARK: - HRV Calculation Methods
    
    func calculateHRVMetrics(_ hrvData: [Double]) -> HRVMetrics {
        guard hrvData.count >= 2 else {
            return HRVMetrics(
                rmssd: 0, sdnn: 0, pnn50: 0, triangularIndex: 0,
                mean: 0, median: 0, range: 0, cv: 0,
                hrvTI: 0, tinn: 0
            )
        }
        
        // 时域分析
        let rmssd = calculateRMSSD(hrvData)
        let sdnn = calculateSDNN(hrvData)
        let pnn50 = calculatePNN50(hrvData)
        let triangularIndex = calculateTriangularIndex(hrvData)
        
        // 统计指标
        let mean = hrvData.reduce(0, +) / Double(hrvData.count)
        let median = calculateMedian(hrvData)
        let range = (hrvData.max() ?? 0) - (hrvData.min() ?? 0)
        let cv = sdnn / mean * 100 // 变异系数百分比
        
        // 几何指标
        let hrvTI = calculateHRVTriangularIndex(hrvData)
        let tinn = calculateTINN(hrvData)
        
        return HRVMetrics(
            rmssd: rmssd,
            sdnn: sdnn,
            pnn50: pnn50,
            triangularIndex: triangularIndex,
            mean: mean,
            median: median,
            range: range,
            cv: cv,
            hrvTI: hrvTI,
            tinn: tinn
        )
    }
    
    func calculateHeartRateMetrics(_ heartRateData: [Double]) -> HeartRateMetrics {
        guard !heartRateData.isEmpty else {
            return HeartRateMetrics(
                average: 0, minimum: 0, maximum: 0, standardDeviation: 0,
                range: 0, percentile25: 0, percentile75: 0, skewness: 0, kurtosis: 0
            )
        }
        
        let sorted = heartRateData.sorted()
        let average = heartRateData.reduce(0, +) / Double(heartRateData.count)
        let minimum = sorted.first ?? 0
        let maximum = sorted.last ?? 0
        
        // 标准差计算
        let variance = heartRateData.map { pow($0 - average, 2) }.reduce(0, +) / Double(heartRateData.count)
        let standardDeviation = sqrt(variance)
        
        // 百分位数
        let percentile25 = calculatePercentile(sorted, percentile: 0.25)
        let percentile75 = calculatePercentile(sorted, percentile: 0.75)
        
        // 偏斜度和峰度
        let skewness = calculateSkewness(heartRateData, mean: average, std: standardDeviation)
        let kurtosis = calculateKurtosis(heartRateData, mean: average, std: standardDeviation)
        
        return HeartRateMetrics(
            average: average,
            minimum: minimum,
            maximum: maximum,
            standardDeviation: standardDeviation,
            range: maximum - minimum,
            percentile25: percentile25,
            percentile75: percentile75,
            skewness: skewness,
            kurtosis: kurtosis
        )
    }
    
    // MARK: - RMSSD (Root Mean Square of Successive Differences)
    
    private func calculateRMSSD(_ data: [Double]) -> Double {
        guard data.count >= 2 else { return 0 }
        
        let differences = zip(data.dropFirst(), data).map { $0.0 - $0.1 }
        let squaredDifferences = differences.map { $0 * $0 }
        let meanSquaredDiff = squaredDifferences.reduce(0, +) / Double(squaredDifferences.count)
        
        return sqrt(meanSquaredDiff)
    }
    
    // MARK: - SDNN (Standard Deviation of NN intervals)
    
    private func calculateSDNN(_ data: [Double]) -> Double {
        guard data.count >= 2 else { return 0 }
        
        let mean = data.reduce(0, +) / Double(data.count)
        let squaredDeviations = data.map { pow($0 - mean, 2) }
        let variance = squaredDeviations.reduce(0, +) / Double(data.count - 1)
        
        return sqrt(variance)
    }
    
    // MARK: - pNN50 (Percentage of successive RR intervals that differ by more than 50ms)
    
    private func calculatePNN50(_ data: [Double]) -> Double {
        guard data.count >= 2 else { return 0 }
        
        let differences = zip(data.dropFirst(), data).map { abs($0.0 - $0.1) }
        let differencesOver50 = differences.filter { $0 > 50.0 }
        
        return Double(differencesOver50.count) / Double(differences.count) * 100.0
    }
    
    // MARK: - Triangular Index
    
    private func calculateTriangularIndex(_ data: [Double]) -> Double {
        guard data.count >= 10 else { return 0 }
        
        // 创建直方图 (bins of 7.8125 ms)
        let binWidth = 7.8125
        let minValue = data.min() ?? 0
        let maxValue = data.max() ?? 0
        let numBins = Int((maxValue - minValue) / binWidth) + 1
        
        var histogram = Array(repeating: 0, count: numBins)
        
        for value in data {
            let binIndex = Int((value - minValue) / binWidth)
            if binIndex < histogram.count {
                histogram[binIndex] += 1
            }
        }
        
        let maxCount = histogram.max() ?? 1
        return Double(data.count) / Double(maxCount)
    }
    
    // MARK: - HRV Triangular Index
    
    private func calculateHRVTriangularIndex(_ data: [Double]) -> Double {
        // 简化版三角指数计算
        let totalCount = data.count
        let binWidth = 7.8125 // 标准HRV分析的bin宽度
        
        // 计算直方图的峰值
        let histogram = createHistogram(data, binWidth: binWidth)
        let maxBinCount = histogram.values.max() ?? 1
        
        return Double(totalCount) / Double(maxBinCount)
    }
    
    // MARK: - TINN (Triangular Interpolation of NN interval histogram)
    
    private func calculateTINN(_ data: [Double]) -> Double {
        guard data.count >= 20 else { return 0 }
        
        let binWidth = 7.8125
        let histogram = createHistogram(data, binWidth: binWidth)
        
        // 寻找直方图的基底宽度
        let sortedCounts = histogram.values.sorted(by: >)
        let threshold = Double(sortedCounts.first ?? 0) * 0.05 // 5%阈值
        
        let significantBins = histogram.filter { $0.value >= Int(threshold) }
        
        if let minBin = significantBins.keys.min(),
           let maxBin = significantBins.keys.max() {
            return (maxBin - minBin) * binWidth
        }
        
        return 0
    }
    
    // MARK: - Statistical Helper Methods
    
    private func calculateMedian(_ data: [Double]) -> Double {
        let sorted = data.sorted()
        let count = sorted.count
        
        if count % 2 == 0 {
            return (sorted[count/2 - 1] + sorted[count/2]) / 2.0
        } else {
            return sorted[count/2]
        }
    }
    
    private func calculatePercentile(_ sortedData: [Double], percentile: Double) -> Double {
        guard !sortedData.isEmpty else { return 0 }
        
        let index = percentile * Double(sortedData.count - 1)
        let lowerIndex = Int(floor(index))
        let upperIndex = Int(ceil(index))
        
        if lowerIndex == upperIndex {
            return sortedData[lowerIndex]
        } else {
            let weight = index - Double(lowerIndex)
            return sortedData[lowerIndex] * (1 - weight) + sortedData[upperIndex] * weight
        }
    }
    
    private func calculateSkewness(_ data: [Double], mean: Double, std: Double) -> Double {
        guard std > 0 && data.count > 2 else { return 0 }
        
        let n = Double(data.count)
        let sum = data.map { pow(($0 - mean) / std, 3) }.reduce(0, +)
        
        return (n / ((n - 1) * (n - 2))) * sum
    }
    
    private func calculateKurtosis(_ data: [Double], mean: Double, std: Double) -> Double {
        guard std > 0 && data.count > 3 else { return 0 }
        
        let n = Double(data.count)
        let sum = data.map { pow(($0 - mean) / std, 4) }.reduce(0, +)
        
        let numerator = (n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))
        let subtraction = (3 * pow(n - 1, 2)) / ((n - 2) * (n - 3))
        
        return numerator * sum - subtraction
    }
    
    private func createHistogram(_ data: [Double], binWidth: Double) -> [Double: Int] {
        let minValue = data.min() ?? 0
        var histogram: [Double: Int] = [:]
        
        for value in data {
            let binKey = floor((value - minValue) / binWidth) * binWidth + minValue
            histogram[binKey, default: 0] += 1
        }
        
        return histogram
    }
    
    // MARK: - Stress Calculation Methods
    
    func calculateRMSSDStress(_ rmssd: Double) -> Double {
        // 基于研究的RMSSD压力评估
        // 正常RMSSD范围: 20-80ms，最佳范围: 35-65ms
        switch rmssd {
        case 0..<15:
            return 0.9 // 很高压力
        case 15..<25:
            return 0.7 // 高压力
        case 25..<35:
            return 0.5 // 中等压力
        case 35..<65:
            return 0.2 // 低压力
        case 65...:
            return 0.1 // 很低压力（可能过度训练）
        default:
            return 0.5
        }
    }
    
    func calculateSDNNStress(_ sdnn: Double) -> Double {
        // SDNN正常范围: 30-100ms
        switch sdnn {
        case 0..<20:
            return 0.9
        case 20..<30:
            return 0.7
        case 30..<50:
            return 0.4
        case 50..<100:
            return 0.2
        case 100...:
            return 0.3 // 可能的异常值
        default:
            return 0.5
        }
    }
    
    func calculateHeartRateStress(_ heartRate: Double) -> Double {
        // 基于静息心率的压力评估
        switch heartRate {
        case 0..<50:
            return 0.3 // 可能过度训练或优秀运动员
        case 50..<60:
            return 0.1 // 很好
        case 60..<70:
            return 0.2 // 好
        case 70..<80:
            return 0.4 // 一般
        case 80..<90:
            return 0.6 // 稍高
        case 90..<100:
            return 0.8 // 高
        case 100...:
            return 0.9 // 很高
        default:
            return 0.5
        }
    }
    
    func calculateHeartRateVariabilityStress(_ variability: Double) -> Double {
        // 心率变异度越高，通常表示压力越大
        switch variability {
        case 0..<5:
            return 0.1 // 很稳定
        case 5..<10:
            return 0.3 // 稳定
        case 10..<15:
            return 0.5 // 中等
        case 15..<20:
            return 0.7 // 不稳定
        case 20...:
            return 0.9 // 很不稳定
        default:
            return 0.5
        }
    }
    
    func calculateRecoveryStress(_ recovery: Double) -> Double {
        // 心率恢复指标（负值表示良好恢复）
        if recovery < 0 {
            return max(0.1, 0.5 + recovery * 0.1) // 恢复良好
        } else {
            return min(0.9, 0.5 + recovery * 0.2) // 恢复较慢
        }
    }
    
    func calculateHRVTrend(_ data: [Double]) -> Double {
        guard data.count >= 2 else { return 0 }
        
        // 简单线性趋势计算
        let indices = Array(0..<data.count).map(Double.init)
        let meanX = indices.reduce(0, +) / Double(indices.count)
        let meanY = data.reduce(0, +) / Double(data.count)
        
        let numerator = zip(indices, data).map { ($0 - meanX) * ($1 - meanY) }.reduce(0, +)
        let denominator = indices.map { pow($0 - meanX, 2) }.reduce(0, +)
        
        return denominator > 0 ? numerator / denominator : 0
    }
    
    func calculateHeartRateRecovery(_ data: [Double]) -> Double {
        guard data.count >= 5 else { return 0 }
        
        // 计算前半段和后半段的平均值差异
        let midPoint = data.count / 2
        let firstHalf = Array(data[0..<midPoint])
        let secondHalf = Array(data[midPoint..<data.count])
        
        let firstHalfMean = firstHalf.reduce(0, +) / Double(firstHalf.count)
        let secondHalfMean = secondHalf.reduce(0, +) / Double(secondHalf.count)
        
        return firstHalfMean - secondHalfMean // 正值表示心率下降（良好恢复）
    }
    
    func analyzeTrend(_ trend: Double) -> Double {
        // 趋势分析：HRV上升趋势通常表示压力减少
        if trend > 0 {
            return max(0.1, 0.5 - trend * 0.1) // HRV上升，压力下降
        } else {
            return min(0.9, 0.5 - trend * 0.1) // HRV下降，压力上升
        }
    }
}