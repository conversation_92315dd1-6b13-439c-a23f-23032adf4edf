import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var scrollOffset: CGFloat = 0
    
    private let privacySections = [
        PrivacySection(
            title: "数据收集",
            icon: "doc.text.fill",
            content: """
StressBalanz仅收集以下必要的健康数据：
• 心率变异性 (HRV) 数据
• 心率数据（静息心率、实时心率）
• 睡眠分析数据
• 运动和健身数据

我们仅收集用于压力分析和健康建议所必需的数据。
"""
        ),
        PrivacySection(
            title: "数据处理",
            icon: "gearshape.fill",
            content: """
所有健康数据的处理均在您的设备本地进行：
• 数据不会上传到我们的服务器
• 不会与第三方分享您的个人健康数据
• 所有计算和分析在您的iPhone和Apple Watch上完成
• 数据存储在iOS的安全沙盒环境中
"""
        ),
        PrivacySection(
            title: "数据安全",
            icon: "lock.shield.fill",
            content: """
我们采用多重措施保护您的数据安全：
• 使用Apple的HealthKit框架，遵循最高安全标准
• 数据在设备上加密存储
• 通过Face ID/Touch ID保护应用访问
• 您可以随时撤销HealthKit访问权限
"""
        ),
        PrivacySection(
            title: "数据控制",
            icon: "slider.horizontal.3",
            content: """
您拥有对数据的完全控制权：
• 可以随时在iPhone设置中管理HealthKit权限
• 可以选择性地授权特定类型的健康数据
• 可以随时删除应用及其所有本地数据
• 可以导出您的压力分析历史记录
"""
        ),
        PrivacySection(
            title: "第三方服务",
            icon: "person.3.fill",
            content: """
StressBalanz不使用任何第三方分析服务：
• 不集成广告SDK
• 不使用数据收集框架
• 不向第三方发送任何个人信息
• 所有功能均为本地实现
"""
        ),
        PrivacySection(
            title: "联系我们",
            icon: "envelope.fill",
            content: """
如果您对隐私政策有任何疑问：
• 邮箱：<EMAIL>
• 我们会在24小时内回复您的隐私相关咨询
• 您有权要求查看我们收集的任何数据
• 您有权要求删除所有相关数据
"""
        )
    ]
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ScrollView {
                    LazyVStack(spacing: 24) {
                        headerSection
                        
                        ForEach(Array(privacySections.enumerated()), id: \.element.title) { index, section in
                            PrivacySectionCard(
                                section: section,
                                index: index
                            )
                            .onAppear {
                                withAnimation(.easeInOut(duration: 0.6).delay(Double(index) * 0.1)) {
                                    // 动画触发
                                }
                            }
                        }
                        
                        footerSection
                        
                        Spacer(minLength: 50)
                    }
                    .padding()
                }
                .coordinateSpace(name: "scroll")
            }
            .background(StressColors.background)
            .navigationTitle("隐私政策")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .foregroundColor(StressColors.primary)
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 20) {
            Image(systemName: "shield.checkerboard")
                .font(.system(size: 60))
                .foregroundColor(StressColors.primary)
                .symbolEffect(.bounce, value: true)
            
            Text("您的隐私，我们的承诺")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(StressColors.textPrimary)
                .multilineTextAlignment(.center)
            
            Text("StressBalanz深信隐私是一项基本人权。我们设计产品时始终将您的隐私放在首位。")
                .font(.body)
                .foregroundColor(StressColors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
        .padding(.vertical, 20)
    }
    
    private var footerSection: some View {
        VStack(spacing: 16) {
            Divider()
                .background(StressColors.textSecondary.opacity(0.3))
            
            VStack(spacing: 12) {
                Text("最后更新：2025年7月13日")
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
                
                Text("版本 1.0")
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
                
                HStack(spacing: 20) {
                    Button("查看使用条款") {
                        // 打开使用条款
                    }
                    .font(.caption)
                    .foregroundColor(StressColors.primary)
                    
                    Button("联系支持") {
                        // 打开邮件应用
                        if let url = URL(string: "mailto:<EMAIL>") {
                            UIApplication.shared.open(url)
                        }
                    }
                    .font(.caption)
                    .foregroundColor(StressColors.primary)
                }
            }
        }
        .padding(.top, 20)
    }
}

struct PrivacySection {
    let title: String
    let icon: String
    let content: String
}

struct PrivacySectionCard: View {
    let section: PrivacySection
    let index: Int
    
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题行
            HStack(spacing: 16) {
                Image(systemName: section.icon)
                    .font(.title2)
                    .foregroundColor(StressColors.primary)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(section.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(StressColors.textPrimary)
                    
                    Text("点击查看详细信息")
                        .font(.caption)
                        .foregroundColor(StressColors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
                    .rotationEffect(.degrees(isExpanded ? 180 : 0))
                    .animation(.easeInOut(duration: 0.3), value: isExpanded)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }
            
            // 内容区域
            if isExpanded {
                Text(section.content)
                    .font(.body)
                    .foregroundColor(StressColors.textSecondary)
                    .lineSpacing(6)
                    .padding(.leading, 46) // 对齐图标
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .move(edge: .top)),
                        removal: .opacity.combined(with: .move(edge: .top))
                    ))
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(StressColors.surface)
                .shadow(color: StressColors.primary.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .scaleEffect(isExpanded ? 1.02 : 1.0)
        .animation(.spring(response: 0.5, dampingFraction: 0.7), value: isExpanded)
    }
}

// MARK: - Privacy Quick Actions View

struct PrivacyQuickActionsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            Text("隐私控制")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(StressColors.textPrimary)
            
            VStack(spacing: 12) {
                PrivacyActionButton(
                    title: "管理HealthKit权限",
                    icon: "heart.circle.fill",
                    description: "在设置中调整健康数据访问权限"
                ) {
                    openHealthKitSettings()
                }
                
                PrivacyActionButton(
                    title: "查看数据使用",
                    icon: "chart.bar.fill",
                    description: "查看应用如何使用您的健康数据"
                ) {
                    // 打开数据使用统计页面
                }
                
                PrivacyActionButton(
                    title: "导出数据",
                    icon: "square.and.arrow.up.fill",
                    description: "导出您的压力分析历史记录"
                ) {
                    // 导出数据功能
                }
                
                PrivacyActionButton(
                    title: "删除所有数据",
                    icon: "trash.fill",
                    description: "永久删除应用中的所有本地数据",
                    isDestructive: true
                ) {
                    // 删除数据确认对话框
                }
            }
            
            Spacer()
            
            Button("关闭") {
                dismiss()
            }
            .foregroundColor(StressColors.textSecondary)
        }
        .padding()
        .background(StressColors.background)
    }
    
    private func openHealthKitSettings() {
        if let settingsURL = URL(string: "x-apple-health://") {
            UIApplication.shared.open(settingsURL)
        } else if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

struct PrivacyActionButton: View {
    let title: String
    let icon: String
    let description: String
    var isDestructive: Bool = false
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isDestructive ? .red : StressColors.primary)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(isDestructive ? .red : StressColors.textPrimary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(StressColors.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
            }
            .padding()
            .background(StressColors.surface)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    PrivacyPolicyView()
}

#Preview("Quick Actions") {
    PrivacyQuickActionsView()
}