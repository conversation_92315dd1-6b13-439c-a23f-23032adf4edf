# “StressBalanz”App 设计概念稿与动效探索

**版本：V1.0**

**目标**：本文档旨在基于PRD，为“StressBalanz”App定义核心的设计语言、视觉关键词及动态交互方向，确保最终产品在视觉和感官体验上与产品愿景保持高度一致，为用户创造一个科学、治愈、沉浸的数字空间。

### 一、 设计概念稿关键词
**1. 核心设计理念：****“无感融入，正向引导”**
* **无感融入 (Seamless Integration)**：设计应如空气般自然，不给用户增加额外的认知负担。功能与交互应符合直觉，让用户专注于自身感受而非操作App。
* **正向引导 (Positive Guidance)**：视觉和交互的每一个细节都应传递积极、平静、充满希望的情感。即使在呈现高压力状态时，也应避免使用引发焦虑的元素，而是传递“这只是一个暂时的状态，我们可以一起改善它”的信号。

⠀**2. 视觉风格关键词：****“呼吸感、流动性、生命力”**
* **呼吸感 (A Sense of Breathing)**：界面布局应有呼吸感，留白充足，元素间距舒适，避免信息过载。动效的节奏应模仿平稳的呼吸，舒缓而有规律。
* **流动性 (Fluidity)**：多使用渐变色、柔和的曲线和无缝的转场动画，营造出一种液体般、能量般流动的视觉感受，象征情绪与压力的非固定性和可变性。
* **生命力 (Vitality)**：视觉元素（如图标、动画）应具有生命感，而非冰冷的几何图形。例如，图标可以有轻微的动态效果，主视觉动画应模拟有机体的形态变化。

⠀**3. 色彩体系：****“情绪色盘”**
* **主色盘**：以 **低饱和度的自然色** 为基础，如黎明的天空蓝（#A8D8EA）、雨后的青草绿（#A9C1A0）、温暖的沙滩米（#F5EFE6）。这些颜色能唤起平静、新生和安全感。
* **压力指示色**：
  * **低压力**：平静的湖水蓝绿渐变。
  * **中等压力**：温暖的日落橙黄渐变，提示注意，但不造成警报感。
  * **高压力**：柔和的晚霞紫粉渐变，取代刺眼的红色。它代表需要被温柔对待的时刻，而非危险警报。
* **辅助色**：用于按钮和高亮的颜色，应从主色盘中提取，保持整体和谐。

⠀**4. 字体与排版：****“清晰、关怀、信赖”**
* **字体选择**：选用圆润、友好的无衬线字体（如苹方-常规体），确保在各种尺寸下的可读性。关键数据（如压力指数）可使用稍粗的字重，但避免尖锐的棱角。
* **排版**：信息层级清晰，标题、正文、说明文字有明显区分。段落行间距适中（建议1.5-1.8倍），创造轻松的阅读体验。

⠀二、 核心动效探索方向
**1. 压力可视化动效：“能量球”方案深化**
这是App的核心，动效必须精致且富有表现力。
* **状态一：平静 (压力指数 0-30)**
  * **形态**：一个完美的、表面光滑的球体，内部有非常缓慢、柔和流动的光晕。
  * **色彩**：湖水蓝绿渐变。
  * **动态**：以极慢的速度进行轻微的、有规律的“呼吸”式缩放。
  * **交互**：触摸时，球体会产生一圈柔和的光波涟漪，并伴有轻微的震动反馈，像触摸平静的水面。
* **状态二：适中 (压力指数 31-70)**
  * **形态**：球体形态依然完整，但内部光晕流动速度加快，表面开始出现一些细微的、流动的粒子效果。
  * **色彩**：逐渐过渡到日落橙黄渐变。
  * **动态**：“呼吸”的频率略微加快，且幅度变得不那么规律。
  * **交互**：触摸时，涟漪扩散更快，粒子会暂时聚集在触摸点。
* **状态三：高压 (压力指数 71-100)**
  * **形态**：球体边缘开始变得模糊、不稳定，出现柔和的“毛刺”或“火焰”状的粒子向外发散。内部光晕流动变得快速甚至有些紊乱。
  * **色彩**：过渡到晚霞紫粉渐变。
  * **动态**：“呼吸”变得短促、不规律，甚至有轻微的抖动。
  * **交互**：触摸时，球体会明显收缩一下，然后释放出更多的粒子，模拟一种敏感、易受惊的状态。

⠀**2. 呼吸训练引导动效**
* **视觉引导**：屏幕中央一个简洁的圆形，根据指令（吸气、屏住、呼气）进行平滑的放大、保持、缩小。圆形的颜色可以与当前呼吸阶段同步变化（如吸气时变亮，呼气时变暗）。
* **触觉同步**：动画的节奏与 Apple Watch 的 Taptic Engine 震动反馈 **完全同步**，形成视觉、触觉合一的沉浸式引导，让用户可以闭上眼睛跟随练习。
* **背景反馈**：训练开始时，背景可以变得模糊或变暗，让用户更专注于呼吸动画。随着训练进行，背景中的压力可视化“能量球”应实时、缓慢地向更平静的状态转变，提供即时的正向反馈。

⠀**3. 微交互与转场动效**
* **原则**：快、轻、自然。
* **按钮交互**：点击按钮时，应有轻微的缩放和颜色变化，给予清晰的反馈，但动画时长不超过0.2秒。
* **页面转场**：避免生硬的左右切换。多使用 **淡入淡出 (Fade)** 和 **向上滑动浮现 (Slide Up)** 的效果。例如，从首页点击进入“数据统计”，统计页面可以从底部平滑地浮上来，返回时再平滑地降下去。
* **数据加载**：当图表或数据加载时，使用骨架屏（Skeleton Screen）并配上轻微的、呼吸感的闪烁动画，而不是旋转的菊花图标，以减少等待的焦虑感。

三、苹果设计规范

- 遵循苹果 App 的设计规范
- 保证深色和浅色模式正常观看
