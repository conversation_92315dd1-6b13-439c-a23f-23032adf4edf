{"permissions": {"allow": ["Bash(find:*)", "Bash(xcodebuild:*)", "mcp__zen__thinkdeep", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "Bash(git add:*)", "Bash(git commit:*)", "mcp__ide__getDiagnostics", "Bash(sudo xcode-select:*)", "Bash(xcrun xcodebuild:*)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -version)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug clean build)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 15 Pro,OS=latest' clean build)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 15 Pro,OS=17.0' clean build)", "Bash(rm:*)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 15 Pro,OS=17.0' build)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug build)", "mcp__zen__chat", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest' build)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 15,OS=17.0' build)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 15,OS=17.0' build -quiet)", "Bash(/Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug -sdk iphonesimulator build)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["zen"]}