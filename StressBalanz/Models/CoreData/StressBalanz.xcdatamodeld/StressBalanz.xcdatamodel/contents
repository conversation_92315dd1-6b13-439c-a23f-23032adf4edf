<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="false" userDefinedModelVersionIdentifier="">
    <entity name="StressReading" representedClassName="StressReading" syncable="YES" codeGenerationType="class">
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="stressLevel" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="hrv" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="heartRate" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="restingHeartRate" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="dataSource" optional="YES" attributeType="String"/>
        <attribute name="confidence" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="UserProfile" inverseName="stressReadings" inverseEntity="UserProfile"/>
    </entity>
    <entity name="UserProfile" representedClassName="UserProfile" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="age" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="gender" optional="YES" attributeType="String"/>
        <attribute name="baselineHRV" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="baselineHeartRate" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="isActive" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <relationship name="stressReadings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="StressReading" inverseName="user" inverseEntity="StressReading"/>
        <relationship name="trainingSessions" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="TrainingSession" inverseName="user" inverseEntity="TrainingSession"/>
    </entity>
    <entity name="TrainingSession" representedClassName="TrainingSession" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="endTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="duration" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="effectiveness" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="preStressLevel" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="postStressLevel" optional="YES" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="UserProfile" inverseName="trainingSessions" inverseEntity="UserProfile"/>
    </entity>
    <entity name="HealthKitData" representedClassName="HealthKitData" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="dataType" attributeType="String"/>
        <attribute name="value" attributeType="Double" usesScalarValueType="YES"/>
        <attribute name="unit" optional="YES" attributeType="String"/>
        <attribute name="metadata" optional="YES" attributeType="Transformable"/>
        <attribute name="sourceBundle" optional="YES" attributeType="String"/>
        <attribute name="isProcessed" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    </entity>
    <elements>
        <element name="StressReading" positionX="-63" positionY="-18" width="128" height="163"/>
        <element name="UserProfile" positionX="-270" positionY="-18" width="128" height="178"/>
        <element name="TrainingSession" positionX="144" positionY="-18" width="128" height="193"/>
        <element name="HealthKitData" positionX="-63" positionY="171" width="128" height="163"/>
    </elements>
</model>