import SwiftUI

struct StressCard: View {
    let title: String
    let content: String
    let stressLevel: Double
    
    private var cardColor: Color {
        switch stressLevel {
        case 0.0..<0.33:
            return StressColors.lowStress
        case 0.33..<0.66:
            return StressColors.mediumStress
        default:
            return StressColors.highStress
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(title)
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                Spacer()
                Circle()
                    .fill(cardColor)
                    .frame(width: 12, height: 12)
            }
            
            Text(content)
                .font(.body)
                .foregroundColor(StressColors.textSecondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

#Preview {
    VStack(spacing: 16) {
        StressCard(
            title: "当前压力",
            content: "轻度压力状态，建议进行短暂休息",
            stressLevel: 0.3
        )
        StressCard(
            title: "中度压力",
            content: "压力水平较高，建议进行呼吸练习",
            stressLevel: 0.6
        )
        StressCard(
            title: "高压力",
            content: "压力水平很高，建议立即进行放松训练",
            stressLevel: 0.9
        )
    }
    .padding()
    .background(StressColors.background)
}