import Foundation

/**
 MultiMetricFusionEngine
 
 多指标融合算法引擎
 将HRV、心率、睡眠质量、环境因素等多个指标融合，
 使用机器学习和统计方法生成综合压力评估
 */
class MultiMetricFusionEngine {
    
    // MARK: - Fusion Configuration
    
    private struct FusionConfig {
        // 动态权重范围
        static let hrvWeightRange: ClosedRange<Double> = 0.3...0.6
        static let heartRateWeightRange: ClosedRange<Double> = 0.2...0.4
        static let sleepWeightRange: ClosedRange<Double> = 0.1...0.3
        static let contextWeightRange: ClosedRange<Double> = 0.05...0.15
        
        // 融合算法参数
        static let adaptiveLearningRate: Double = 0.1
        static let confidenceThreshold: Double = 0.7
        static let temporalWeightDecay: Double = 0.95 // 历史数据权重衰减
        static let outlierThreshold: Double = 2.5 // Z-score阈值
    }
    
    // MARK: - Fusion Methods
    
    enum FusionMethod {
        case weightedAverage        // 加权平均
        case bayesianFusion        // 贝叶斯融合
        case fuzzyLogic            // 模糊逻辑
        case neuralNetwork         // 神经网络（简化版）
        case adaptiveWeighting     // 自适应权重
    }
    
    // MARK: - Fusion Result
    
    struct FusionResult {
        let fusedStressLevel: Double
        let confidence: Double
        let method: FusionMethod
        let weights: MetricWeights
        let individualContributions: [String: Double]
        let qualityMetrics: QualityMetrics
        let timestamp: Date
        
        struct QualityMetrics {
            let dataCompleteness: Double    // 数据完整性 0-1
            let temporalConsistency: Double // 时间一致性 0-1
            let crossValidation: Double     // 交叉验证得分 0-1
            let uncertaintyLevel: Double    // 不确定性水平 0-1
        }
    }
    
    struct MetricWeights {
        let hrv: Double
        let heartRate: Double
        let sleep: Double
        let context: Double
        let temporal: Double // 时间因素权重
        
        var isNormalized: Bool {
            let sum = hrv + heartRate + sleep + context + temporal
            return abs(sum - 1.0) < 0.001
        }
        
        func normalized() -> MetricWeights {
            let sum = hrv + heartRate + sleep + context + temporal
            guard sum > 0 else { return self }
            
            return MetricWeights(
                hrv: hrv / sum,
                heartRate: heartRate / sum,
                sleep: sleep / sum,
                context: context / sum,
                temporal: temporal / sum
            )
        }
    }
    
    // MARK: - Historical Data Management
    
    private var historicalResults: [FusionResult] = []
    private var adaptiveWeights: MetricWeights
    private let maxHistorySize = 100
    
    init() {
        // 初始化自适应权重
        self.adaptiveWeights = MetricWeights(
            hrv: 0.4,
            heartRate: 0.3,
            sleep: 0.2,
            context: 0.1,
            temporal: 0.0
        )
    }
    
    // MARK: - Main Fusion Method
    
    func fuseMetrics(
        hrvAnalysis: StressAnalysisEngine.StressAnalysisResult?,
        heartRateAnalysis: HeartRateAnalysis?,
        sleepAnalysis: SleepAnalysis?,
        contextAnalysis: ContextAnalysis?,
        historicalData: [StressAnalysisEngine.StressAnalysisResult] = [],
        method: FusionMethod = .adaptiveWeighting
    ) -> FusionResult {
        
        // 1. 数据预处理和验证
        let preprocessedData = preprocessInputData(
            hrv: hrvAnalysis,
            heartRate: heartRateAnalysis,
            sleep: sleepAnalysis,
            context: contextAnalysis
        )
        
        // 2. 计算数据质量指标
        let qualityMetrics = calculateQualityMetrics(
            hrv: hrvAnalysis,
            heartRate: heartRateAnalysis,
            sleep: sleepAnalysis,
            context: contextAnalysis,
            historical: historicalData
        )
        
        // 3. 根据方法进行融合
        let fusionResult: (stressLevel: Double, confidence: Double, weights: MetricWeights)
        
        switch method {
        case .weightedAverage:
            fusionResult = performWeightedAverageFusion(preprocessedData)
        case .bayesianFusion:
            fusionResult = performBayesianFusion(preprocessedData)
        case .fuzzyLogic:
            fusionResult = performFuzzyLogicFusion(preprocessedData)
        case .neuralNetwork:
            fusionResult = performNeuralNetworkFusion(preprocessedData)
        case .adaptiveWeighting:
            fusionResult = performAdaptiveWeightingFusion(preprocessedData, historical: historicalData)
        }
        
        // 4. 计算个体贡献
        let contributions = calculateIndividualContributions(
            preprocessedData,
            weights: fusionResult.weights
        )
        
        // 5. 时间因素调整
        let temporalAdjustedResult = applyTemporalAdjustment(
            stressLevel: fusionResult.stressLevel,
            historical: historicalData
        )
        
        // 6. 创建最终结果
        let result = FusionResult(
            fusedStressLevel: temporalAdjustedResult,
            confidence: fusionResult.confidence * qualityMetrics.dataCompleteness,
            method: method,
            weights: fusionResult.weights,
            individualContributions: contributions,
            qualityMetrics: qualityMetrics,
            timestamp: Date()
        )
        
        // 7. 更新历史记录和自适应权重
        updateHistoricalData(result)
        updateAdaptiveWeights(result)
        
        return result
    }
    
    // MARK: - Preprocessing
    
    private func preprocessInputData(
        hrv: StressAnalysisEngine.StressAnalysisResult?,
        heartRate: HeartRateAnalysis?,
        sleep: SleepAnalysis?,
        context: ContextAnalysis?
    ) -> ProcessedMetricData {
        
        var processedData = ProcessedMetricData()
        
        // HRV数据处理
        if let hrvData = hrv, hrvData.isReliable {
            processedData.hrvStress = normalizeStressValue(hrvData.stressLevel)
            processedData.hrvConfidence = hrvData.confidence
            processedData.hasHRV = true
        }
        
        // 心率数据处理
        if let hrData = heartRate, hrData.isValid {
            processedData.heartRateStress = normalizeStressValue(hrData.stressLevel)
            processedData.heartRateConfidence = hrData.confidence
            processedData.hasHeartRate = true
        }
        
        // 睡眠数据处理
        if let sleepData = sleep, sleepData.isValid {
            processedData.sleepStress = normalizeStressValue(sleepData.stressImpact)
            processedData.sleepConfidence = sleepData.confidence
            processedData.hasSleep = true
        }
        
        // 上下文数据处理
        if let contextData = context, contextData.isValid {
            processedData.contextStress = normalizeStressValue(contextData.stressModifier)
            processedData.contextConfidence = contextData.confidence
            processedData.hasContext = true
        }
        
        return processedData
    }
    
    // MARK: - Fusion Algorithms
    
    private func performWeightedAverageFusion(_ data: ProcessedMetricData) -> (stressLevel: Double, confidence: Double, weights: MetricWeights) {
        
        var totalStress: Double = 0.0
        var totalWeight: Double = 0.0
        var totalConfidence: Double = 0.0
        
        let weights = calculateDynamicWeights(data)
        
        if data.hasHRV {
            totalStress += data.hrvStress * weights.hrv
            totalWeight += weights.hrv
            totalConfidence += data.hrvConfidence * weights.hrv
        }
        
        if data.hasHeartRate {
            totalStress += data.heartRateStress * weights.heartRate
            totalWeight += weights.heartRate
            totalConfidence += data.heartRateConfidence * weights.heartRate
        }
        
        if data.hasSleep {
            totalStress += data.sleepStress * weights.sleep
            totalWeight += weights.sleep
            totalConfidence += data.sleepConfidence * weights.sleep
        }
        
        if data.hasContext {
            totalStress += data.contextStress * weights.context
            totalWeight += weights.context
            totalConfidence += data.contextConfidence * weights.context
        }
        
        let finalStress = totalWeight > 0 ? totalStress / totalWeight : 0.5
        let finalConfidence = totalWeight > 0 ? totalConfidence / totalWeight : 0.0
        
        return (finalStress, finalConfidence, weights)
    }
    
    private func performBayesianFusion(_ data: ProcessedMetricData) -> (stressLevel: Double, confidence: Double, weights: MetricWeights) {
        
        // 简化贝叶斯融合
        let prior = 0.5 // 先验概率
        var posterior = prior
        var totalEvidence: Double = 0.0
        
        let weights = calculateDynamicWeights(data)
        
        // 贝叶斯更新
        if data.hasHRV {
            let likelihood = data.hrvStress
            let evidence = data.hrvConfidence
            posterior = updateBayesianPosterior(posterior, likelihood: likelihood, evidence: evidence)
            totalEvidence += evidence * weights.hrv
        }
        
        if data.hasHeartRate {
            let likelihood = data.heartRateStress
            let evidence = data.heartRateConfidence
            posterior = updateBayesianPosterior(posterior, likelihood: likelihood, evidence: evidence)
            totalEvidence += evidence * weights.heartRate
        }
        
        if data.hasSleep {
            let likelihood = data.sleepStress
            let evidence = data.sleepConfidence
            posterior = updateBayesianPosterior(posterior, likelihood: likelihood, evidence: evidence)
            totalEvidence += evidence * weights.sleep
        }
        
        return (posterior, totalEvidence, weights)
    }
    
    private func performFuzzyLogicFusion(_ data: ProcessedMetricData) -> (stressLevel: Double, confidence: Double, weights: MetricWeights) {
        
        // 模糊逻辑融合
        var fuzzyStress: Double = 0.0
        var fuzzyConfidence: Double = 0.0
        
        let weights = calculateDynamicWeights(data)
        
        // 定义模糊集合
        let lowStress = createFuzzySet(center: 0.2, width: 0.3)
        let mediumStress = createFuzzySet(center: 0.5, width: 0.3)
        let highStress = createFuzzySet(center: 0.8, width: 0.3)
        
        // 模糊推理
        var ruleStrengths: [Double] = []
        
        if data.hasHRV && data.hasHeartRate {
            // 规则1: 如果HRV低且心率高，则压力高
            let hrvLow = evaluateFuzzySet(highStress, value: data.hrvStress) // HRV压力高=HRV值低
            let hrHigh = evaluateFuzzySet(highStress, value: data.heartRateStress)
            let ruleStrength = min(hrvLow, hrHigh)
            ruleStrengths.append(ruleStrength)
            fuzzyStress += ruleStrength * 0.8
        }
        
        if data.hasHRV && data.hasSleep {
            // 规则2: 如果HRV正常且睡眠好，则压力低
            let hrvNormal = evaluateFuzzySet(mediumStress, value: data.hrvStress)
            let sleepGood = evaluateFuzzySet(lowStress, value: data.sleepStress)
            let ruleStrength = min(hrvNormal, sleepGood)
            ruleStrengths.append(ruleStrength)
            fuzzyStress += ruleStrength * 0.2
        }
        
        // 归一化
        let totalRuleStrength = ruleStrengths.reduce(0, +)
        if totalRuleStrength > 0 {
            fuzzyStress /= totalRuleStrength
            fuzzyConfidence = totalRuleStrength / Double(ruleStrengths.count)
        } else {
            fuzzyStress = 0.5
            fuzzyConfidence = 0.0
        }
        
        return (fuzzyStress, fuzzyConfidence, weights)
    }
    
    private func performNeuralNetworkFusion(_ data: ProcessedMetricData) -> (stressLevel: Double, confidence: Double, weights: MetricWeights) {
        
        // 简化神经网络（单层感知机）
        let weights = calculateDynamicWeights(data)
        
        // 输入层
        var inputs: [Double] = []
        if data.hasHRV { inputs.append(data.hrvStress) }
        if data.hasHeartRate { inputs.append(data.heartRateStress) }
        if data.hasSleep { inputs.append(data.sleepStress) }
        if data.hasContext { inputs.append(data.contextStress) }
        
        // 权重矩阵（预训练的简化权重）
        let neuralWeights = [weights.hrv, weights.heartRate, weights.sleep, weights.context]
        
        // 前向传播
        var output: Double = 0.0
        for (i, input) in inputs.enumerated() {
            if i < neuralWeights.count {
                output += input * neuralWeights[i]
            }
        }
        
        // 激活函数（Sigmoid）
        let activatedOutput = 1.0 / (1.0 + exp(-output * 2.0 + 1.0))
        
        // 计算置信度
        let variance = inputs.map { pow($0 - output, 2) }.reduce(0, +) / Double(inputs.count)
        let confidence = max(0.0, 1.0 - variance)
        
        return (activatedOutput, confidence, weights)
    }
    
    private func performAdaptiveWeightingFusion(_ data: ProcessedMetricData, historical: [StressAnalysisEngine.StressAnalysisResult]) -> (stressLevel: Double, confidence: Double, weights: MetricWeights) {
        
        // 基于历史数据调整权重
        let adaptedWeights = adaptWeightsBasedOnHistory(data, historical: historical)
        
        // 执行加权融合
        var totalStress: Double = 0.0
        var totalWeight: Double = 0.0
        var totalConfidence: Double = 0.0
        
        if data.hasHRV {
            totalStress += data.hrvStress * adaptedWeights.hrv
            totalWeight += adaptedWeights.hrv
            totalConfidence += data.hrvConfidence * adaptedWeights.hrv
        }
        
        if data.hasHeartRate {
            totalStress += data.heartRateStress * adaptedWeights.heartRate
            totalWeight += adaptedWeights.heartRate
            totalConfidence += data.heartRateConfidence * adaptedWeights.heartRate
        }
        
        if data.hasSleep {
            totalStress += data.sleepStress * adaptedWeights.sleep
            totalWeight += adaptedWeights.sleep
            totalConfidence += data.sleepConfidence * adaptedWeights.sleep
        }
        
        if data.hasContext {
            totalStress += data.contextStress * adaptedWeights.context
            totalWeight += adaptedWeights.context
            totalConfidence += data.contextConfidence * adaptedWeights.context
        }
        
        let finalStress = totalWeight > 0 ? totalStress / totalWeight : 0.5
        let finalConfidence = totalWeight > 0 ? totalConfidence / totalWeight : 0.0
        
        return (finalStress, finalConfidence, adaptedWeights)
    }
    
    // MARK: - Helper Methods
    
    private func normalizeStressValue(_ value: Double) -> Double {
        return max(0.0, min(1.0, value))
    }
    
    private func calculateDynamicWeights(_ data: ProcessedMetricData) -> MetricWeights {
        var weights = adaptiveWeights
        
        // 根据数据可用性调整权重
        let availableMetrics = [data.hasHRV, data.hasHeartRate, data.hasSleep, data.hasContext].filter { $0 }.count
        
        if availableMetrics == 0 {
            return MetricWeights(hrv: 0, heartRate: 0, sleep: 0, context: 0, temporal: 0)
        }
        
        // 重新分配权重
        if !data.hasHRV { weights = MetricWeights(hrv: 0, heartRate: weights.heartRate, sleep: weights.sleep, context: weights.context, temporal: 0) }
        if !data.hasHeartRate { weights = MetricWeights(hrv: weights.hrv, heartRate: 0, sleep: weights.sleep, context: weights.context, temporal: 0) }
        if !data.hasSleep { weights = MetricWeights(hrv: weights.hrv, heartRate: weights.heartRate, sleep: 0, context: weights.context, temporal: 0) }
        if !data.hasContext { weights = MetricWeights(hrv: weights.hrv, heartRate: weights.heartRate, sleep: weights.sleep, context: 0, temporal: 0) }
        
        return weights.normalized()
    }
    
    private func calculateQualityMetrics(
        hrv: StressAnalysisEngine.StressAnalysisResult?,
        heartRate: HeartRateAnalysis?,
        sleep: SleepAnalysis?,
        context: ContextAnalysis?,
        historical: [StressAnalysisEngine.StressAnalysisResult]
    ) -> FusionResult.QualityMetrics {
        
        let dataCompleteness = calculateDataCompleteness(hrv: hrv, heartRate: heartRate, sleep: sleep, context: context)
        let temporalConsistency = calculateTemporalConsistency(historical: historical)
        let crossValidation = performCrossValidation(hrv: hrv, heartRate: heartRate)
        let uncertaintyLevel = calculateUncertaintyLevel(hrv: hrv, heartRate: heartRate, sleep: sleep)
        
        return FusionResult.QualityMetrics(
            dataCompleteness: dataCompleteness,
            temporalConsistency: temporalConsistency,
            crossValidation: crossValidation,
            uncertaintyLevel: uncertaintyLevel
        )
    }
    
    private func calculateDataCompleteness(hrv: StressAnalysisEngine.StressAnalysisResult?, heartRate: HeartRateAnalysis?, sleep: SleepAnalysis?, context: ContextAnalysis?) -> Double {
        let totalMetrics = 4.0
        var availableMetrics = 0.0
        
        if hrv?.isReliable == true { availableMetrics += 1.0 }
        if heartRate?.isValid == true { availableMetrics += 1.0 }
        if sleep?.isValid == true { availableMetrics += 1.0 }
        if context?.isValid == true { availableMetrics += 1.0 }
        
        return availableMetrics / totalMetrics
    }
    
    private func calculateTemporalConsistency(historical: [StressAnalysisEngine.StressAnalysisResult]) -> Double {
        guard historical.count >= 3 else { return 0.5 }
        
        let recentValues = historical.prefix(5).map { $0.stressLevel }
        let variance = calculateVariance(recentValues)
        
        // 方差越小，一致性越高
        return max(0.0, 1.0 - variance * 2.0)
    }
    
    private func performCrossValidation(hrv: StressAnalysisEngine.StressAnalysisResult?, heartRate: HeartRateAnalysis?) -> Double {
        guard let hrvStress = hrv?.stressLevel,
              let hrStress = heartRate?.stressLevel else { return 0.5 }
        
        // 计算HRV和心率指标的相关性
        let difference = abs(hrvStress - hrStress)
        return max(0.0, 1.0 - difference * 2.0)
    }
    
    private func calculateUncertaintyLevel(hrv: StressAnalysisEngine.StressAnalysisResult?, heartRate: HeartRateAnalysis?, sleep: SleepAnalysis?) -> Double {
        var uncertainties: [Double] = []
        
        if let hrvConf = hrv?.confidence { uncertainties.append(1.0 - hrvConf) }
        if let hrConf = heartRate?.confidence { uncertainties.append(1.0 - hrConf) }
        if let sleepConf = sleep?.confidence { uncertainties.append(1.0 - sleepConf) }
        
        guard !uncertainties.isEmpty else { return 1.0 }
        
        return uncertainties.reduce(0, +) / Double(uncertainties.count)
    }
    
    private func calculateVariance(_ values: [Double]) -> Double {
        guard values.count > 1 else { return 0.0 }
        
        let mean = values.reduce(0, +) / Double(values.count)
        let squaredDifferences = values.map { pow($0 - mean, 2) }
        return squaredDifferences.reduce(0, +) / Double(values.count - 1)
    }
    
    // 其他helper方法将在下一个文件中继续实现...
}

// MARK: - Supporting Structures

struct ProcessedMetricData {
    var hrvStress: Double = 0.0
    var hrvConfidence: Double = 0.0
    var hasHRV: Bool = false
    
    var heartRateStress: Double = 0.0
    var heartRateConfidence: Double = 0.0
    var hasHeartRate: Bool = false
    
    var sleepStress: Double = 0.0
    var sleepConfidence: Double = 0.0
    var hasSleep: Bool = false
    
    var contextStress: Double = 0.0
    var contextConfidence: Double = 0.0
    var hasContext: Bool = false
}

struct HeartRateAnalysis {
    let stressLevel: Double
    let confidence: Double
    let isValid: Bool
}

struct SleepAnalysis {
    let stressImpact: Double
    let confidence: Double
    let isValid: Bool
}

struct ContextAnalysis {
    let stressModifier: Double
    let confidence: Double
    let isValid: Bool
}