import HealthKit
import Foundation
import SwiftUI

class HealthKitManager: ObservableObject, @unchecked Sendable {
    private let healthStore = HKHealthStore()
    private let dataValidationService = DataValidationService()
    private let dataCache: HealthKitDataCache
    
    @Published var isAuthorized = false
    @Published var authorizationStatus: HKAuthorizationStatus = .notDetermined
    @Published var latestHRV: Double?
    @Published var latestHeartRate: Double?
    @Published var lastValidationReport: ValidationReport?
    
    private var observerQueries: [HKObserverQuery] = []
    
    private let healthKitTypes: Set<HKObjectType> = {
        guard let hrvType = HKObjectType.quantityType(forIdentifier: .heartRateVariabilitySDNN),
              let heartRateType = HKObjectType.quantityType(forIdentifier: .heartRate),
              let restingHeartRateType = HKObjectType.quantityType(forIdentifier: .restingHeartRate),
              let standingHeartRateType = HKObjectType.quantityType(forIdentifier: .heartRateRecoveryOneMinute),
              let sleepAnalysisType = HKObjectType.categoryType(forIdentifier: .sleepAnalysis) else {
            return Set<HKObjectType>()
        }
        
        let workoutType = HKObjectType.workoutType()
        
        return [
            hrvType,
            heartRateType,
            restingHeartRateType,
            standingHeartRateType,
            sleepAnalysisType,
            workoutType
        ]
    }()
    
    init() {
        // 初始化数据缓存
        self.dataCache = HealthKitDataCache(context: PersistenceController.shared.container.viewContext)
        
        checkHealthKitAvailability()
        setupCacheNotifications()
    }
    
    private func checkHealthKitAvailability() {
        guard HKHealthStore.isHealthDataAvailable() else {
            print("HealthKit不可用")
            return
        }
        
        checkAuthorizationStatus()
    }
    
    private func setupCacheNotifications() {
        NotificationCenter.default.addObserver(
            forName: .cachePreloadRequested,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.preloadRecentHealthData()
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .cacheSyncRequested,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let syncInfo = notification.object as? [String: Date],
               let startDate = syncInfo["startDate"],
               let endDate = syncInfo["endDate"] {
                Task {
                    await self?.syncHealthDataToCache(from: startDate, to: endDate)
                }
            }
        }
    }
    
    private func checkAuthorizationStatus() {
        for type in healthKitTypes {
            let status = healthStore.authorizationStatus(for: type)
            if status == .notDetermined {
                authorizationStatus = .notDetermined
                isAuthorized = false
                return
            }
        }
        
        let allAuthorized = healthKitTypes.allSatisfy { type in
            healthStore.authorizationStatus(for: type) == .sharingAuthorized
        }
        
        isAuthorized = allAuthorized
        authorizationStatus = allAuthorized ? .sharingAuthorized : .sharingDenied
    }
    
    func requestHealthKitPermission() async -> Bool {
        guard HKHealthStore.isHealthDataAvailable() else {
            print("HealthKit不可用")
            return false
        }
        
        do {
            try await healthStore.requestAuthorization(toShare: Set<HKSampleType>(), read: healthKitTypes)
            
            await MainActor.run {
                checkAuthorizationStatus()
            }
            
            return isAuthorized
        } catch {
            print("HealthKit授权失败: \(error.localizedDescription)")
            return false
        }
    }
    
    func getAuthorizationStatus(for identifier: HKQuantityTypeIdentifier) -> HKAuthorizationStatus {
        guard let quantityType = HKQuantityType.quantityType(forIdentifier: identifier) else {
            return .notDetermined
        }
        return healthStore.authorizationStatus(for: quantityType)
    }
    
    func getAuthorizationStatus(for identifier: HKCategoryTypeIdentifier) -> HKAuthorizationStatus {
        guard let categoryType = HKCategoryType.categoryType(forIdentifier: identifier) else {
            return .notDetermined
        }
        return healthStore.authorizationStatus(for: categoryType)
    }
}

// MARK: - HRV Data Reading
extension HealthKitManager {
    
    func fetchLatestHRV() async -> HKQuantitySample? {
        guard let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            print("无法创建HRV类型")
            return nil
        }
        
        return await withCheckedContinuation { continuation in
            let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
            let query = HKSampleQuery(
                sampleType: hrvType,
                predicate: nil,
                limit: 1,
                sortDescriptors: [sortDescriptor]
            ) { [weak self] _, samples, error in
                if let error = error {
                    print("获取HRV数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                    return
                }
                
                if let hrvSample = samples?.first as? HKQuantitySample {
                    let hrvValue = hrvSample.quantity.doubleValue(for: HKUnit.secondUnit(with: .milli))
                    
                    // 验证数据质量
                    Task {
                        let validationResult = self?.dataValidationService.validateHRVData([hrvSample])
                        await MainActor.run {
                            if let result = validationResult, result.confidence > 0.7 {
                                self?.latestHRV = hrvValue
                            } else {
                                print("HRV数据质量较低: \(validationResult?.confidence ?? 0.0)")
                            }
                        }
                    }
                    
                    continuation.resume(returning: hrvSample)
                } else {
                    continuation.resume(returning: nil)
                }
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchHRVData(from startDate: Date, to endDate: Date) async -> [HKQuantitySample] {
        // 首先尝试从缓存获取数据
        let cachedData = await dataCache.getCachedHRVData(from: startDate, to: endDate)
        
        // 如果缓存有足够的数据，优先使用缓存
        if !cachedData.isEmpty {
            let cacheStartDate = cachedData.map { $0.timestamp! }.min() ?? Date()
            let cacheEndDate = cachedData.map { $0.timestamp! }.max() ?? Date()
            
            // 检查缓存覆盖范围是否足够
            let cacheRange = cacheEndDate.timeIntervalSince(cacheStartDate)
            let requestedRange = endDate.timeIntervalSince(startDate)
            
            if cacheRange >= requestedRange * 0.8 { // 如果缓存覆盖80%以上，使用缓存
                print("使用HRV缓存数据，共\(cachedData.count)条")
                return convertCachedDataToHKSamples(cachedData, dataType: .heartRateVariabilitySDNN)
            }
        }
        
        // 从HealthKit获取新数据
        guard let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            print("无法创建HRV类型")
            return []
        }
        
        let predicate = HKQuery.predicateForSamples(
            withStart: startDate,
            end: endDate,
            options: .strictStartDate
        )
        
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: hrvType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [sortDescriptor]
            ) { [weak self] _, samples, error in
                if let error = error {
                    print("获取HRV数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: [])
                    return
                }
                
                let hrvSamples = samples as? [HKQuantitySample] ?? []
                
                // 缓存新获取的数据
                Task {
                    await self?.dataCache.cacheHRVData(hrvSamples)
                }
                
                continuation.resume(returning: hrvSamples)
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchHRVStatistics(from startDate: Date, to endDate: Date) async -> HKStatistics? {
        guard let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            print("无法创建HRV类型")
            return nil
        }
        
        let predicate = HKQuery.predicateForSamples(
            withStart: startDate,
            end: endDate,
            options: .strictStartDate
        )
        
        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(
                quantityType: hrvType,
                quantitySamplePredicate: predicate,
                options: [.discreteAverage, .discreteMin, .discreteMax]
            ) { _, statistics, error in
                if let error = error {
                    print("获取HRV统计数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                    return
                }
                
                continuation.resume(returning: statistics)
            }
            
            healthStore.execute(query)
        }
    }
}

// MARK: - Heart Rate Data Reading
extension HealthKitManager {
    
    func fetchLatestHeartRate() async -> HKQuantitySample? {
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate) else {
            print("无法创建心率类型")
            return nil
        }
        
        return await withCheckedContinuation { continuation in
            let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
            let query = HKSampleQuery(
                sampleType: heartRateType,
                predicate: nil,
                limit: 1,
                sortDescriptors: [sortDescriptor]
            ) { [weak self] _, samples, error in
                if let error = error {
                    print("获取心率数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                    return
                }
                
                if let heartRateSample = samples?.first as? HKQuantitySample {
                    let heartRateValue = heartRateSample.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
                    
                    DispatchQueue.main.async {
                        self?.latestHeartRate = heartRateValue
                    }
                    
                    continuation.resume(returning: heartRateSample)
                } else {
                    continuation.resume(returning: nil)
                }
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchHeartRateData(from startDate: Date, to endDate: Date) async -> [HKQuantitySample] {
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate) else {
            print("无法创建心率类型")
            return []
        }
        
        let predicate = HKQuery.predicateForSamples(
            withStart: startDate,
            end: endDate,
            options: .strictStartDate
        )
        
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: heartRateType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [sortDescriptor]
            ) { _, samples, error in
                if let error = error {
                    print("获取心率数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: [])
                    return
                }
                
                let heartRateSamples = samples as? [HKQuantitySample] ?? []
                continuation.resume(returning: heartRateSamples)
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchRestingHeartRate() async -> Double? {
        guard let restingHeartRateType = HKQuantityType.quantityType(forIdentifier: .restingHeartRate) else {
            print("无法创建静息心率类型")
            return nil
        }
        
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: restingHeartRateType,
                predicate: nil,
                limit: 1,
                sortDescriptors: [sortDescriptor]
            ) { _, samples, error in
                if let error = error {
                    print("获取静息心率数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                    return
                }
                
                if let restingHeartRateSample = samples?.first as? HKQuantitySample {
                    let value = restingHeartRateSample.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
                    continuation.resume(returning: value)
                } else {
                    continuation.resume(returning: nil)
                }
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchHeartRateStatistics(from startDate: Date, to endDate: Date) async -> HKStatistics? {
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate) else {
            print("无法创建心率类型")
            return nil
        }
        
        let predicate = HKQuery.predicateForSamples(
            withStart: startDate,
            end: endDate,
            options: .strictStartDate
        )
        
        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(
                quantityType: heartRateType,
                quantitySamplePredicate: predicate,
                options: [.discreteAverage, .discreteMin, .discreteMax]
            ) { _, statistics, error in
                if let error = error {
                    print("获取心率统计数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                    return
                }
                
                continuation.resume(returning: statistics)
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchHeartRateZoneData(from startDate: Date, to endDate: Date, restingHeartRate: Double) async -> HeartRateZoneData {
        let heartRateData = await fetchHeartRateData(from: startDate, to: endDate)
        let maxHeartRate = 220.0 - 30.0 // 假设用户30岁，实际应该从UserProfile获取
        
        var zoneData = HeartRateZoneData()
        
        for sample in heartRateData {
            let heartRate = sample.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
            let zone = calculateHeartRateZone(heartRate: heartRate, restingHR: restingHeartRate, maxHR: maxHeartRate)
            
            switch zone {
            case .resting:
                zoneData.restingZoneMinutes += 1
            case .fatBurn:
                zoneData.fatBurnZoneMinutes += 1
            case .aerobic:
                zoneData.aerobicZoneMinutes += 1
            case .anaerobic:
                zoneData.anaerobicZoneMinutes += 1
            case .peak:
                zoneData.peakZoneMinutes += 1
            }
        }
        
        return zoneData
    }
    
    private func calculateHeartRateZone(heartRate: Double, restingHR: Double, maxHR: Double) -> HeartRateZone {
        let hrReserve = maxHR - restingHR
        let percentage = (heartRate - restingHR) / hrReserve
        
        switch percentage {
        case ..<0.5:
            return .resting
        case 0.5..<0.6:
            return .fatBurn
        case 0.6..<0.7:
            return .aerobic
        case 0.7..<0.85:
            return .anaerobic
        default:
            return .peak
        }
    }
}

enum HeartRateZone {
    case resting
    case fatBurn
    case aerobic
    case anaerobic
    case peak
}

struct HeartRateZoneData {
    var restingZoneMinutes: Int = 0
    var fatBurnZoneMinutes: Int = 0
    var aerobicZoneMinutes: Int = 0
    var anaerobicZoneMinutes: Int = 0
    var peakZoneMinutes: Int = 0
}

// MARK: - Sleep Data Reading
extension HealthKitManager {
    
    func fetchSleepData(from startDate: Date, to endDate: Date) async -> [HKCategorySample] {
        guard let sleepType = HKCategoryType.categoryType(forIdentifier: .sleepAnalysis) else {
            print("无法创建睡眠分析类型")
            return []
        }
        
        let predicate = HKQuery.predicateForSamples(
            withStart: startDate,
            end: endDate,
            options: .strictStartDate
        )
        
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: sleepType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [sortDescriptor]
            ) { _, samples, error in
                if let error = error {
                    print("获取睡眠数据失败: \(error.localizedDescription)")
                    continuation.resume(returning: [])
                    return
                }
                
                let sleepSamples = samples as? [HKCategorySample] ?? []
                continuation.resume(returning: sleepSamples)
            }
            
            healthStore.execute(query)
        }
    }
    
    func analyzeSleepQuality(from startDate: Date, to endDate: Date) async -> SleepQualityData {
        let sleepSamples = await fetchSleepData(from: startDate, to: endDate)
        var qualityData = SleepQualityData()
        
        for sample in sleepSamples {
            let duration = sample.endDate.timeIntervalSince(sample.startDate) / 3600.0 // 转换为小时
            
            switch sample.value {
            case HKCategoryValueSleepAnalysis.inBed.rawValue:
                qualityData.timeInBed += duration
            case HKCategoryValueSleepAnalysis.asleepCore.rawValue:
                qualityData.coreSleeep += duration
            case HKCategoryValueSleepAnalysis.asleepDeep.rawValue:
                qualityData.deepSleep += duration
            case HKCategoryValueSleepAnalysis.asleepREM.rawValue:
                qualityData.remSleep += duration
            case HKCategoryValueSleepAnalysis.awake.rawValue:
                qualityData.awakeTime += duration
            default:
                break
            }
        }
        
        // 计算睡眠效率
        let totalSleepTime = qualityData.coreSleeep + qualityData.deepSleep + qualityData.remSleep
        if qualityData.timeInBed > 0 {
            qualityData.sleepEfficiency = (totalSleepTime / qualityData.timeInBed) * 100
        }
        
        // 计算睡眠质量评分 (0-100)
        qualityData.qualityScore = calculateSleepQualityScore(data: qualityData)
        
        return qualityData
    }
    
    private func calculateSleepQualityScore(data: SleepQualityData) -> Double {
        var score: Double = 0
        
        // 总睡眠时间评分 (0-40分)
        let totalSleep = data.coreSleeep + data.deepSleep + data.remSleep
        if totalSleep >= 7.0 && totalSleep <= 9.0 {
            score += 40
        } else if totalSleep >= 6.0 && totalSleep <= 10.0 {
            score += 30
        } else if totalSleep >= 5.0 && totalSleep <= 11.0 {
            score += 20
        } else {
            score += 10
        }
        
        // 深度睡眠比例评分 (0-30分)
        if totalSleep > 0 {
            let deepSleepPercentage = (data.deepSleep / totalSleep) * 100
            if deepSleepPercentage >= 15 && deepSleepPercentage <= 25 {
                score += 30
            } else if deepSleepPercentage >= 10 && deepSleepPercentage <= 30 {
                score += 20
            } else if deepSleepPercentage >= 5 && deepSleepPercentage <= 35 {
                score += 10
            }
        }
        
        // 睡眠效率评分 (0-30分)
        if data.sleepEfficiency >= 85 {
            score += 30
        } else if data.sleepEfficiency >= 75 {
            score += 20
        } else if data.sleepEfficiency >= 65 {
            score += 10
        }
        
        return min(score, 100)
    }
    
    func fetchLastNightSleep() async -> SleepQualityData? {
        let calendar = Calendar.current
        let now = Date()
        
        // 计算昨晚的睡眠时间范围 (前一天的18:00到今天的12:00)
        guard let endOfYesterday = calendar.date(byAdding: .day, value: -1, to: now),
              let startTime = calendar.date(bySettingHour: 18, minute: 0, second: 0, of: endOfYesterday),
              let endTime = calendar.date(bySettingHour: 12, minute: 0, second: 0, of: now) else {
            return nil
        }
        
        let sleepData = await analyzeSleepQuality(from: startTime, to: endTime)
        return sleepData.coreSleeep > 0 ? sleepData : nil
    }
}

struct SleepQualityData {
    var timeInBed: Double = 0.0         // 在床时间 (小时)
    var coreSleeep: Double = 0.0        // 核心睡眠时间 (小时)
    var deepSleep: Double = 0.0         // 深度睡眠时间 (小时)
    var remSleep: Double = 0.0          // REM睡眠时间 (小时)
    var awakeTime: Double = 0.0         // 清醒时间 (小时)
    var sleepEfficiency: Double = 0.0   // 睡眠效率 (百分比)
    var qualityScore: Double = 0.0      // 睡眠质量评分 (0-100)
    
    var totalSleepTime: Double {
        return coreSleeep + deepSleep + remSleep
    }
    
    var sleepQualityLevel: SleepQualityLevel {
        switch qualityScore {
        case 80...100:
            return .excellent
        case 60..<80:
            return .good
        case 40..<60:
            return .fair
        case 20..<40:
            return .poor
        default:
            return .veryPoor
        }
    }
}

enum SleepQualityLevel: String, CaseIterable {
    case excellent = "优秀"
    case good = "良好"
    case fair = "一般"
    case poor = "较差"
    case veryPoor = "很差"
    
    var color: Color {
        switch self {
        case .excellent:
            return StressColors.lowStress
        case .good:
            return Color.green
        case .fair:
            return StressColors.mediumStress
        case .poor:
            return Color.orange
        case .veryPoor:
            return StressColors.highStress
        }
    }
}

// MARK: - Real-time Data Monitoring
extension HealthKitManager {
    
    func startRealTimeMonitoring() {
        guard isAuthorized else {
            print("HealthKit未授权，无法开始实时监听")
            return
        }
        
        startHRVMonitoring()
        startHeartRateMonitoring()
        startSleepMonitoring()
    }
    
    func stopRealTimeMonitoring() {
        observerQueries.forEach { query in
            healthStore.stop(query)
        }
        observerQueries.removeAll()
    }
    
    private func startHRVMonitoring() {
        guard let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            print("无法创建HRV类型")
            return
        }
        
        let query = HKObserverQuery(sampleType: hrvType, predicate: nil) { [weak self] query, completionHandler, error in
            if let error = error {
                print("HRV监听失败: \(error.localizedDescription)")
                completionHandler()
                return
            }
            
            Task {
                await self?.handleNewHRVData()
                completionHandler()
            }
        }
        
        healthStore.execute(query)
        observerQueries.append(query)
        
        // 启用后台传递
        healthStore.enableBackgroundDelivery(for: hrvType, frequency: .immediate) { success, error in
            if let error = error {
                print("启用HRV后台传递失败: \(error.localizedDescription)")
            } else {
                print("HRV后台传递已启用")
            }
        }
    }
    
    private func startHeartRateMonitoring() {
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate) else {
            print("无法创建心率类型")
            return
        }
        
        let query = HKObserverQuery(sampleType: heartRateType, predicate: nil) { [weak self] query, completionHandler, error in
            if let error = error {
                print("心率监听失败: \(error.localizedDescription)")
                completionHandler()
                return
            }
            
            Task {
                await self?.handleNewHeartRateData()
                completionHandler()
            }
        }
        
        healthStore.execute(query)
        observerQueries.append(query)
        
        // 启用后台传递
        healthStore.enableBackgroundDelivery(for: heartRateType, frequency: .immediate) { success, error in
            if let error = error {
                print("启用心率后台传递失败: \(error.localizedDescription)")
            } else {
                print("心率后台传递已启用")
            }
        }
    }
    
    private func startSleepMonitoring() {
        guard let sleepType = HKCategoryType.categoryType(forIdentifier: .sleepAnalysis) else {
            print("无法创建睡眠分析类型")
            return
        }
        
        let query = HKObserverQuery(sampleType: sleepType, predicate: nil) { [weak self] query, completionHandler, error in
            if let error = error {
                print("睡眠监听失败: \(error.localizedDescription)")
                completionHandler()
                return
            }
            
            Task {
                await self?.handleNewSleepData()
                completionHandler()
            }
        }
        
        healthStore.execute(query)
        observerQueries.append(query)
        
        // 启用后台传递
        healthStore.enableBackgroundDelivery(for: sleepType, frequency: .daily) { success, error in
            if let error = error {
                print("启用睡眠后台传递失败: \(error.localizedDescription)")
            } else {
                print("睡眠后台传递已启用")
            }
        }
    }
    
    @MainActor
    private func handleNewHRVData() async {
        _ = await fetchLatestHRV()
        // 更新UI或触发压力分析
        NotificationCenter.default.post(name: .newHRVDataReceived, object: latestHRV)
    }
    
    @MainActor
    private func handleNewHeartRateData() async {
        _ = await fetchLatestHeartRate()
        // 更新UI或触发压力分析
        NotificationCenter.default.post(name: .newHeartRateDataReceived, object: latestHeartRate)
    }
    
    @MainActor
    private func handleNewSleepData() async {
        // 获取最新睡眠数据并分析
        if let sleepData = await fetchLastNightSleep() {
            NotificationCenter.default.post(name: .newSleepDataReceived, object: sleepData)
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let newHRVDataReceived = Notification.Name("newHRVDataReceived")
    static let newHeartRateDataReceived = Notification.Name("newHeartRateDataReceived")
    static let newSleepDataReceived = Notification.Name("newSleepDataReceived")
    static let healthKitAuthorizationChanged = Notification.Name("healthKitAuthorizationChanged")
    static let dataValidationCompleted = Notification.Name("dataValidationCompleted")
}

extension HealthKitManager {
    static let shared = HealthKitManager()
}

// MARK: - Data Validation Integration
extension HealthKitManager {
    
    func validateCurrentHealthData() async {
        guard isAuthorized else {
            print("HealthKit未授权，无法验证数据")
            return
        }
        
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .hour, value: -24, to: endDate) ?? endDate
        
        async let hrvData = fetchHRVData(from: startDate, to: endDate)
        async let heartRateData = fetchHeartRateData(from: startDate, to: endDate)
        async let sleepData = fetchLastNightSleep()
        
        let hrvSamples = await hrvData
        let heartRateSamples = await heartRateData
        let sleepQuality = await sleepData
        
        var validationResults: [DataValidationService.ValidationResult] = []
        
        // 验证HRV数据
        if !hrvSamples.isEmpty {
            let hrvValidation = dataValidationService.validateHRVData(hrvSamples)
            validationResults.append(hrvValidation)
        }
        
        // 验证心率数据
        if !heartRateSamples.isEmpty {
            let heartRateValidation = dataValidationService.validateHeartRateData(heartRateSamples)
            validationResults.append(heartRateValidation)
        }
        
        // 验证睡眠数据
        if let sleepQuality = sleepQuality {
            let sleepValidation = dataValidationService.validateSleepData(sleepQuality)
            validationResults.append(sleepValidation)
        }
        
        // 生成综合报告
        if !validationResults.isEmpty {
            let report = dataValidationService.generateValidationReport(for: validationResults)
            
            await MainActor.run {
                self.lastValidationReport = report
                
                // 发布验证结果通知
                NotificationCenter.default.post(
                    name: .dataValidationCompleted,
                    object: report
                )
                
                // 记录验证结果
                print("数据验证完成 - 总体信心度: \(String(format: "%.2f", report.overallConfidence))")
                if report.hasSignificantIssues {
                    print("发现重要数据质量问题，建议检查设备状态")
                }
            }
        }
    }
    
    func getDataQualityStatus() -> DataValidationService.DataQuality {
        return lastValidationReport?.qualityLevel ?? .invalid
    }
    
    func getDataQualityRecommendations() -> [String] {
        return lastValidationReport?.recommendations ?? []
    }
    
    // MARK: - Cache Integration Methods
    
    private func convertCachedDataToHKSamples(_ cachedData: [HealthKitData], dataType: HKQuantityTypeIdentifier) -> [HKQuantitySample] {
        // 注意：这是一个简化的转换，实际使用中可能需要更复杂的逻辑
        // 因为HKSample是只读的，无法直接创建，这里返回空数组
        // 在实际应用中，应该直接使用缓存数据而不是转换回HKSample
        print("警告：缓存数据转换为HKSample暂未实现，建议直接使用缓存数据")
        return []
    }
    
    private func preloadRecentHealthData() async {
        await dataCache.preloadRecentData()
    }
    
    private func syncHealthDataToCache(from startDate: Date, to endDate: Date) async {
        // 获取并缓存HRV数据
        let hrvSamples = await fetchHRVDataDirectly(from: startDate, to: endDate)
        await dataCache.cacheHRVData(hrvSamples)
        
        // 获取并缓存心率数据
        let heartRateSamples = await fetchHeartRateDataDirectly(from: startDate, to: endDate)
        await dataCache.cacheHeartRateData(heartRateSamples)
        
        print("健康数据同步到缓存完成")
    }
    
    private func fetchHRVDataDirectly(from startDate: Date, to endDate: Date) async -> [HKQuantitySample] {
        guard let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            return []
        }
        
        let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate, options: .strictStartDate)
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: hrvType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [sortDescriptor]
            ) { _, samples, error in
                continuation.resume(returning: samples as? [HKQuantitySample] ?? [])
            }
            healthStore.execute(query)
        }
    }
    
    private func fetchHeartRateDataDirectly(from startDate: Date, to endDate: Date) async -> [HKQuantitySample] {
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate) else {
            return []
        }
        
        let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate, options: .strictStartDate)
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(
                sampleType: heartRateType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [sortDescriptor]
            ) { _, samples, error in
                continuation.resume(returning: samples as? [HKQuantitySample] ?? [])
            }
            healthStore.execute(query)
        }
    }
    
    // MARK: - Cache Management Public Methods
    
    func getCacheStatistics() async -> HealthKitDataCache.CacheStatistics {
        return await dataCache.getCacheStatistics()
    }
    
    func optimizeCache() async {
        await dataCache.optimizeCache()
    }
    
    func clearCache() async {
        await dataCache.clearAllCache()
    }
}