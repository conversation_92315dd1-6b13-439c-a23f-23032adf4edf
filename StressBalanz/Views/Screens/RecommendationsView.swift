import SwiftUI

struct RecommendationsView: View {
    @EnvironmentObject private var stressAnalysisService: StressAnalysisService
    @Environment(\.dismiss) private var dismiss
    @State private var personalizedRecommendations: [PersonalizedRecommendation] = []
    @State private var selectedRecommendation: PersonalizedRecommendation?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    currentRecommendationsSection
                    personalizedRecommendationsSection
                    emergencySection
                }
                .padding()
            }
            .background(StressColors.background)
            .navigationTitle("个性化建议")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .foregroundColor(StressColors.primary)
                }
            }
            .onAppear {
                loadPersonalizedRecommendations()
            }
        }
    }
    
    private var currentRecommendationsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("当前建议")
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Spacer()
                
                Text("基于实时分析")
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
            }
            
            if stressAnalysisService.recommendations.isEmpty {
                EmptyRecommendationsView()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(Array(stressAnalysisService.recommendations.enumerated()), id: \.offset) { index, recommendation in
                        CurrentRecommendationCard(
                            recommendation: recommendation,
                            index: index
                        )
                    }
                }
            }
        }
    }
    
    private var personalizedRecommendationsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("个性化建议")
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Spacer()
                
                Button("刷新") {
                    loadPersonalizedRecommendations()
                }
                .font(.caption)
                .foregroundColor(StressColors.primary)
            }
            
            if personalizedRecommendations.isEmpty {
                ProgressView("生成个性化建议中...")
                    .foregroundColor(StressColors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(personalizedRecommendations, id: \.title) { recommendation in
                        PersonalizedRecommendationCard(
                            recommendation: recommendation
                        ) {
                            selectedRecommendation = recommendation
                        }
                    }
                }
            }
        }
    }
    
    private var emergencySection: some View {
        VStack(spacing: 16) {
            Text("紧急情况处理")
                .font(.headline)
                .foregroundColor(StressColors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                EmergencyActionCard(
                    title: "立即放松",
                    description: "4-7-8呼吸法：吸气4秒，憋气7秒，呼气8秒",
                    icon: "lungs.fill",
                    color: .blue
                ) {
                    // 启动呼吸练习
                }
                
                EmergencyActionCard(
                    title: "寻求帮助",
                    description: "如果压力持续过高，建议联系专业心理健康服务",
                    icon: "phone.fill",
                    color: .red
                ) {
                    // 打开紧急联系人或热线
                }
                
                EmergencyActionCard(
                    title: "记录状态",
                    description: "记录当前感受和可能的压力源，有助于后续分析",
                    icon: "note.text",
                    color: .orange
                ) {
                    // 打开日志记录
                }
            }
        }
    }
    
    private func loadPersonalizedRecommendations() {
        personalizedRecommendations = stressAnalysisService.generatePersonalizedRecommendations()
    }
}

struct CurrentRecommendationCard: View {
    let recommendation: String
    let index: Int
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            VStack {
                Image(systemName: "lightbulb.fill")
                    .font(.title3)
                    .foregroundColor(StressColors.primary)
                
                Text("\(index + 1)")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(StressColors.textSecondary)
            }
            .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 8) {
                Text(recommendation)
                    .font(.body)
                    .foregroundColor(StressColors.textPrimary)
                    .multilineTextAlignment(.leading)
                
                HStack {
                    Text("实时建议")
                        .font(.caption2)
                        .foregroundColor(StressColors.textSecondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(StressColors.primary.opacity(0.2))
                        .cornerRadius(8)
                    
                    Spacer()
                }
            }
            
            Spacer()
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(16)
    }
}

struct PersonalizedRecommendationCard: View {
    let recommendation: PersonalizedRecommendation
    let onTap: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: iconForRecommendationType(recommendation.type))
                    .font(.title3)
                    .foregroundColor(recommendation.priority.color)
                
                Text(recommendation.title)
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Spacer()
                
                PriorityBadge(priority: recommendation.priority)
            }
            
            Text(recommendation.description)
                .font(.body)
                .foregroundColor(StressColors.textSecondary)
                .multilineTextAlignment(.leading)
            
            HStack {
                if recommendation.estimatedDuration > 0 {
                    Label(formatDuration(recommendation.estimatedDuration), systemImage: "clock")
                        .font(.caption)
                        .foregroundColor(StressColors.textSecondary)
                }
                
                Spacer()
                
                Button("开始") {
                    onTap()
                }
                .font(.caption)
                .foregroundColor(StressColors.primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(StressColors.primary.opacity(0.2))
                .cornerRadius(12)
            }
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(16)
        .onTapGesture {
            onTap()
        }
    }
    
    private func iconForRecommendationType(_ type: PersonalizedRecommendation.RecommendationType) -> String {
        switch type {
        case .breathing: return "lungs.fill"
        case .mindfulness: return "brain.head.profile"
        case .exercise: return "figure.run"
        case .nutrition: return "fork.knife"
        case .sleep: return "bed.double.fill"
        case .social: return "person.2.fill"
        case .break: return "pause.circle.fill"
        case .lifestyle: return "leaf.fill"
        case .schedule: return "calendar"
        case .intervention: return "exclamationmark.triangle.fill"
        case .maintenance: return "checkmark.circle.fill"
        case .emergency: return "cross.circle.fill"
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration / 60)
        if minutes < 60 {
            return "\(minutes)分钟"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(remainingMinutes)分钟"
            }
        }
    }
}

struct PriorityBadge: View {
    let priority: PersonalizedRecommendation.Priority
    
    var body: some View {
        Text(priorityText)
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(priority.color)
            .cornerRadius(8)
    }
    
    private var priorityText: String {
        switch priority {
        case .low: return "低"
        case .medium: return "中"
        case .high: return "高"
        case .urgent: return "紧急"
        }
    }
}

struct EmergencyActionCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            VStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
            }
            .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
            
            Button(action: action) {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(color)
            }
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
    }
}

struct EmptyRecommendationsView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.circle.fill")
                .font(.largeTitle)
                .foregroundColor(StressColors.lowStress)
            
            Text("当前状态良好")
                .font(.headline)
                .foregroundColor(StressColors.textPrimary)
            
            Text("您的压力水平处于健康范围内，继续保持良好的生活习惯")
                .font(.body)
                .foregroundColor(StressColors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(16)
    }
}

#Preview {
    RecommendationsView()
}