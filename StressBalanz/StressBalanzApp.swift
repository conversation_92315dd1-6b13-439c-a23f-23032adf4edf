import SwiftUI

@main
struct StressBalanzApp: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var healthKitManager = HealthKitManager.shared
    @StateObject private var stressAnalysisService: StressAnalysisService
    
    init() {
        let healthKitManager = HealthKitManager.shared
        let stressAnalysisService = StressAnalysisService(
            healthKitManager: healthKitManager,
            persistenceController: PersistenceController.shared
        )
        
        self._healthKitManager = StateObject(wrappedValue: healthKitManager)
        self._stressAnalysisService = StateObject(wrappedValue: stressAnalysisService)
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(healthKitManager)
                .environmentObject(stressAnalysisService)
                .onReceive(NotificationCenter.default.publisher(for: .healthKitAuthorizationChanged)) { _ in
                    if healthKitManager.isAuthorized {
                        healthKitManager.startRealTimeMonitoring()
                        stressAnalysisService.startContinuousAnalysis()
                    } else {
                        stressAnalysisService.stopContinuousAnalysis()
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: .stressAnalysisCompleted)) { notification in
                    if let analysisRecord = notification.object as? StressAnalysisService.AnalysisRecord {
                        print("压力分析完成: \(analysisRecord.stressLevel)")
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: .emergencyStressDetected)) { notification in
                    if let analysisRecord = notification.object as? StressAnalysisService.AnalysisRecord {
                        print("⚠️ 紧急压力警报: \(analysisRecord.stressLevel)")
                        // 可以在这里触发紧急通知或UI警告
                    }
                }
                .onAppear {
                    if healthKitManager.isAuthorized {
                        healthKitManager.startRealTimeMonitoring()
                        stressAnalysisService.startContinuousAnalysis()
                    }
                }
        }
    }
}
