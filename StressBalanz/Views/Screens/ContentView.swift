//
//  ContentView.swift
//  StressBalanz
//
//  Created by 刘青 on 2025/7/12.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var healthKitManager: HealthKitManager
    @EnvironmentObject private var stressAnalysisService: StressAnalysisService
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \StressReading.timestamp, ascending: false)],
        animation: .default)
    private var stressReadings: FetchedResults<StressReading>
    
    @State private var showingHealthKitPermission = false
    @State private var showingPrivacyPolicy = false
    @State private var showingStressInsights = false
    
    var body: some View {
        NavigationView {
            ZStack {
                StressColors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        headerSection
                        healthKitStatusSection
                        stressAnalysisSection
                        quickActionsSection
                        recentDataSection
                    }
                    .padding()
                }
            }
            .navigationTitle("StressBalanz")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("压力洞察", systemImage: "chart.line.uptrend.xyaxis") {
                            showingStressInsights = true
                        }
                        
                        Button("手动分析", systemImage: "waveform.path.ecg") {
                            Task {
                                await stressAnalysisService.performManualAnalysis()
                            }
                        }
                        
                        Button("更新基线", systemImage: "person.crop.circle.badge.checkmark") {
                            Task {
                                await stressAnalysisService.refreshPersonalBaseline()
                            }
                        }
                        
                        Divider()
                        
                        Button("隐私政策", systemImage: "lock.shield") {
                            showingPrivacyPolicy = true
                        }
                        
                        Button("设置", systemImage: "gear") {
                            // 打开设置页面
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(StressColors.primary)
                    }
                }
            }
            .sheet(isPresented: $showingHealthKitPermission) {
                HealthKitPermissionView {
                    // 权限授权后的回调
                }
            }
            .sheet(isPresented: $showingPrivacyPolicy) {
                PrivacyPolicyView()
            }
            .sheet(isPresented: $showingStressInsights) {
                StressInsightsView()
                    .environmentObject(stressAnalysisService)
            }
        }
        .onAppear {
            // 视图出现时的初始化已在App层面处理
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Text("欢迎回来")
                .font(.title2)
                .foregroundColor(StressColors.textSecondary)
            
            Text("压力管理助手")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(StressColors.textPrimary)
        }
    }
    
    private var healthKitStatusSection: some View {
        Group {
            if !healthKitManager.isAuthorized {
                StressCard(
                    title: "HealthKit未授权",
                    content: "点击下方按钮授权访问健康数据以获得更准确的压力分析",
                    stressLevel: 0.8
                )
                .onTapGesture {
                    showingHealthKitPermission = true
                }
            } else {
                StressCard(
                    title: "HealthKit已连接",
                    content: "正在从Apple健康应用获取数据进行压力分析",
                    stressLevel: 0.2
                )
            }
        }
    }
    
    private var stressAnalysisSection: some View {
        VStack(spacing: 16) {
            if stressAnalysisService.isAnalyzing {
                StressCard(
                    title: "正在分析压力水平",
                    content: "正在处理HealthKit数据并分析您的压力状态...",
                    stressLevel: 0.5
                )
            } else {
                StressCard(
                    title: "当前压力水平",
                    content: stressLevelDescription,
                    stressLevel: stressAnalysisService.currentStressLevel
                )
                
                StressProgressView(
                    progress: stressAnalysisService.currentStressLevel,
                    title: "压力评估",
                    subtitle: confidenceDescription
                )
                
                if let lastAnalysis = stressAnalysisService.lastAnalysisTime {
                    Text("最后更新: \(lastAnalysis.formatted(date: .omitted, time: .shortened))")
                        .font(.caption)
                        .foregroundColor(StressColors.textSecondary)
                }
            }
        }
    }
    
    private var stressLevelDescription: String {
        let level = stressAnalysisService.stressClassification
        
        switch level {
        case .veryLow:
            return "压力水平很低，状态非常好"
        case .low:
            return "压力水平较低，状态良好"
        case .moderate:
            return "压力水平中等，建议适当关注"
        case .high:
            return "压力水平较高，建议进行放松"
        case .veryHigh:
            return "压力水平很高，建议立即休息"
        }
    }
    
    private var confidenceDescription: String {
        let confidence = stressAnalysisService.analysisConfidence
        let dataQuality = stressAnalysisService.getStressInsights().dataQuality
        
        if confidence >= 0.8 {
            return "分析置信度高，基于\(dataQuality.rawValue)质量数据"
        } else if confidence >= 0.6 {
            return "分析置信度中等，建议收集更多数据"
        } else {
            return "分析置信度较低，数据可能不足"
        }
    }
    
    private var quickActionsSection: some View {
        VStack(spacing: 16) {
            Text("快速操作")
                .font(.headline)
                .foregroundColor(StressColors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 16) {
                PrimaryButton(title: "开始训练") {
                    startTraining()
                }
                
                SecondaryButton(title: healthKitManager.isAuthorized ? "查看分析" : "授权HealthKit") {
                    if healthKitManager.isAuthorized {
                        viewAnalytics()
                    } else {
                        showingHealthKitPermission = true
                    }
                }
            }
        }
    }
    
    private var recentDataSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("最近的数据")
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                Spacer()
                Button("查看全部") {
                    viewAllData()
                }
                .foregroundColor(StressColors.primary)
            }
            
            if stressReadings.isEmpty {
                Text(healthKitManager.isAuthorized ? "暂无数据" : "授权HealthKit后查看真实数据")
                    .foregroundColor(StressColors.textSecondary)
                    .padding()
            } else {
                ForEach(stressReadings.prefix(3), id: \.timestamp) { reading in
                    StressCard(
                        title: "压力记录",
                        content: "\(reading.timestamp?.formatted(date: .abbreviated, time: .shortened) ?? "")",
                        stressLevel: reading.stressLevel
                    )
                }
            }
        }
    }
    
    private func startTraining() {
        print("开始训练")
    }
    
    private func viewAnalytics() {
        showingStressInsights = true
    }
    
    private func viewAllData() {
        print("查看全部数据")
    }
}

#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
        .environmentObject(HealthKitManager.shared)
}
