import SwiftUI
import Charts

struct StressInsightsView: View {
    @EnvironmentObject private var stressAnalysisService: StressAnalysisService
    @Environment(\.dismiss) private var dismiss
    @State private var selectedPeriod: StatisticsPeriod = .week
    @State private var showingRecommendations = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    overviewSection
                    chartSection
                    statisticsSection
                    recommendationsSection
                    dataQualitySection
                }
                .padding()
            }
            .background(StressColors.background)
            .navigationTitle("压力洞察")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(StressColors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("导出数据", systemImage: "square.and.arrow.up") {
                            exportData()
                        }
                        
                        But<PERSON>("刷新分析", systemImage: "arrow.clockwise") {
                            Task {
                                await stressAnalysisService.performManualAnalysis()
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(StressColors.primary)
                    }
                }
            }
            .sheet(isPresented: $showingRecommendations) {
                RecommendationsView()
                    .environmentObject(stressAnalysisService)
            }
        }
    }
    
    private var overviewSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前状态")
                        .font(.headline)
                        .foregroundColor(StressColors.textPrimary)
                    
                    Text(stressAnalysisService.stressClassification.description)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(getStressLevelColor(stressAnalysisService.currentStressLevel))
                    
                    Text(String(format: "%.1f%%", stressAnalysisService.currentStressLevel * 100))
                        .font(.subheadline)
                        .foregroundColor(StressColors.textSecondary)
                }
                
                Spacer()
                
                CircularProgressView(
                    progress: stressAnalysisService.currentStressLevel,
                    size: 80,
                    strokeWidth: 8,
                    color: getStressLevelColor(stressAnalysisService.currentStressLevel)
                )
            }
            .padding()
            .background(StressColors.surface)
            .cornerRadius(16)
            
            if let baseline = stressAnalysisService.personalBaseline {
                BaselineComparisonView(baseline: baseline, currentLevel: stressAnalysisService.currentStressLevel)
            }
        }
    }
    
    private var chartSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("压力趋势")
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Spacer()
                
                Picker("时间段", selection: $selectedPeriod) {
                    Text("今天").tag(StatisticsPeriod.today)
                    Text("本周").tag(StatisticsPeriod.week)
                    Text("本月").tag(StatisticsPeriod.month)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            StressTrendChart(
                records: stressAnalysisService.getAnalysisHistory(days: daysPeriod(selectedPeriod)),
                period: selectedPeriod
            )
            .frame(height: 200)
            .padding()
            .background(StressColors.surface)
            .cornerRadius(16)
        }
    }
    
    private var statisticsSection: some View {
        let statistics = stressAnalysisService.getStressStatistics(for: selectedPeriod)
        
        return VStack(spacing: 16) {
            Text("统计信息")
                .font(.headline)
                .foregroundColor(StressColors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatisticCard(
                    title: "平均水平",
                    value: String(format: "%.1f%%", statistics.averageLevel * 100),
                    color: getStressLevelColor(statistics.averageLevel)
                )
                
                StatisticCard(
                    title: "变化趋势",
                    value: statistics.trend.description,
                    color: statistics.trend.color
                )
                
                StatisticCard(
                    title: "数据质量",
                    value: String(format: "%.0f%%", statistics.qualityScore * 100),
                    color: getDataQualityColor(statistics.qualityScore)
                )
                
                StatisticCard(
                    title: "分析次数",
                    value: "\(statistics.dataPoints)",
                    color: StressColors.primary
                )
            }
        }
    }
    
    private var recommendationsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("个性化建议")
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Spacer()
                
                Button("查看全部") {
                    showingRecommendations = true
                }
                .foregroundColor(StressColors.primary)
            }
            
            LazyVStack(spacing: 12) {
                ForEach(Array(stressAnalysisService.recommendations.prefix(3).enumerated()), id: \.offset) { index, recommendation in
                    RecommendationCard(recommendation: recommendation, index: index)
                }
            }
        }
    }
    
    private var dataQualitySection: some View {
        let insights = stressAnalysisService.getStressInsights()
        
        return VStack(spacing: 16) {
            Text("数据质量")
                .font(.headline)
                .foregroundColor(StressColors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                DataQualityRow(
                    title: "分析置信度",
                    value: String(format: "%.0f%%", insights.confidence * 100),
                    color: getConfidenceColor(insights.confidence)
                )
                
                DataQualityRow(
                    title: "数据完整性",
                    value: insights.dataQuality.rawValue,
                    color: getDataQualityColor(insights.confidence)
                )
                
                if let lastUpdate = stressAnalysisService.lastAnalysisTime {
                    DataQualityRow(
                        title: "最后更新",
                        value: formatTimestamp(lastUpdate),
                        color: StressColors.textSecondary
                    )
                }
            }
            .padding()
            .background(StressColors.surface)
            .cornerRadius(16)
        }
    }
    
    // MARK: - Helper Methods
    
    private func daysPeriod(_ period: StatisticsPeriod) -> Int {
        switch period {
        case .today: return 1
        case .week: return 7
        case .month: return 30
        case .quarter: return 90
        }
    }
    
    private func getStressLevelColor(_ level: Double) -> Color {
        switch level {
        case 0.0..<0.2: return StressColors.lowStress
        case 0.2..<0.4: return Color.green
        case 0.4..<0.6: return StressColors.mediumStress
        case 0.6..<0.8: return Color.orange
        default: return StressColors.highStress
        }
    }
    
    private func getConfidenceColor(_ confidence: Double) -> Color {
        switch confidence {
        case 0.8...1.0: return Color.green
        case 0.6..<0.8: return Color.yellow
        case 0.4..<0.6: return Color.orange
        default: return Color.red
        }
    }
    
    private func getDataQualityColor(_ quality: Double) -> Color {
        return getConfidenceColor(quality)
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    private func exportData() {
        Task {
            if let data = await stressAnalysisService.exportAnalysisData(format: .json) {
                // 在实际应用中，这里应该保存到文件或分享
                print("导出了 \(data.count) 字节的数据")
            }
        }
    }
}

// MARK: - Supporting Views

struct CircularProgressView: View {
    let progress: Double
    let size: CGFloat
    let strokeWidth: CGFloat
    let color: Color
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: strokeWidth)
            
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(color, style: StrokeStyle(lineWidth: strokeWidth, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1), value: progress)
            
            Text(String(format: "%.0f%%", progress * 100))
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
        .frame(width: size, height: size)
    }
}

struct BaselineComparisonView: View {
    let baseline: PersonalBaselineCalibrationEngine.PersonalBaseline
    let currentLevel: Double
    
    var body: some View {
        VStack(spacing: 8) {
            Text("与个人基线对比")
                .font(.subheadline)
                .foregroundColor(StressColors.textSecondary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("个人基线")
                        .font(.caption)
                        .foregroundColor(StressColors.textSecondary)
                    Text(String(format: "%.1f%%", baseline.stressResponsePattern.baselineStressLevel * 100))
                        .font(.headline)
                        .foregroundColor(StressColors.textPrimary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("当前偏差")
                        .font(.caption)
                        .foregroundColor(StressColors.textSecondary)
                    
                    let deviation = currentLevel - baseline.stressResponsePattern.baselineStressLevel
                    Text(String(format: "%+.1f%%", deviation * 100))
                        .font(.headline)
                        .foregroundColor(deviation > 0 ? Color.red : Color.green)
                }
            }
        }
        .padding()
        .background(StressColors.surface.opacity(0.5))
        .cornerRadius(12)
    }
}

struct StatisticCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(StressColors.textSecondary)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(12)
    }
}

struct RecommendationCard: View {
    let recommendation: String
    let index: Int
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(index + 1)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(StressColors.primary)
                .frame(width: 20, height: 20)
                .background(StressColors.primary.opacity(0.2))
                .cornerRadius(10)
            
            Text(recommendation)
                .font(.body)
                .foregroundColor(StressColors.textPrimary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(12)
    }
}

struct DataQualityRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .foregroundColor(StressColors.textSecondary)
            
            Spacer()
            
            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

struct StressTrendChart: View {
    let records: [StressAnalysisService.AnalysisRecord]
    let period: StatisticsPeriod
    
    var body: some View {
        VStack {
            if records.isEmpty {
                VStack {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.largeTitle)
                        .foregroundColor(StressColors.textSecondary)
                    Text("暂无数据")
                        .foregroundColor(StressColors.textSecondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 简化的图表实现
                // 在实际应用中应该使用Charts框架
                SimpleLineChart(data: records.map { ($0.timestamp, $0.stressLevel) })
            }
        }
    }
}

struct SimpleLineChart: View {
    let data: [(Date, Double)]
    
    var body: some View {
        GeometryReader { geometry in
            let width = geometry.size.width
            let height = geometry.size.height
            
            if data.count > 1 {
                Path { path in
                    for (index, point) in data.enumerated() {
                        let x = CGFloat(index) / CGFloat(data.count - 1) * width
                        let y = height - (CGFloat(point.1) * height)
                        
                        if index == 0 {
                            path.move(to: CGPoint(x: x, y: y))
                        } else {
                            path.addLine(to: CGPoint(x: x, y: y))
                        }
                    }
                }
                .stroke(StressColors.primary, lineWidth: 2)
            }
        }
    }
}

#Preview {
    StressInsightsView()
}