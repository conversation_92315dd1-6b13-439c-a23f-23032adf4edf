import SwiftUI

struct PrimaryButton: View {
    let title: String
    let action: () -> Void
    let isLoading: Bool
    
    init(title: String, isLoading: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.isLoading = isLoading
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity, minHeight: 50)
            .background(StressColors.primary)
            .cornerRadius(25)
            .shadow(color: StressColors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .disabled(isLoading)
        .scaleEffect(isLoading ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
}

struct SecondaryButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(StressColors.primary)
                .frame(maxWidth: .infinity, minHeight: 50)
                .background(Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(StressColors.primary, lineWidth: 2)
                )
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        PrimaryButton(title: "开始训练") {
            print("Primary button tapped")
        }
        
        PrimaryButton(title: "加载中...", isLoading: true) {
            print("Loading button tapped")
        }
        
        SecondaryButton(title: "查看历史") {
            print("Secondary button tapped")
        }
    }
    .padding()
    .background(StressColors.background)
}