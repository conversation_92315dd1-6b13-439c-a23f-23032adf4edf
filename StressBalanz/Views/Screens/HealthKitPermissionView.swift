import SwiftUI
import HealthKit

struct HealthKitPermissionView: View {
    @StateObject private var healthKitManager = HealthKitManager.shared
    @State private var showingPermissionAlert = false
    @State private var isRequestingPermission = false
    @Environment(\.dismiss) private var dismiss
    
    let onPermissionGranted: () -> Void
    
    private let permissionItems = [
        PermissionItem(
            icon: "heart.fill",
            title: "心率变异性 (HRV)",
            description: "监测压力水平的核心指标",
            color: StressColors.primary
        ),
        PermissionItem(
            icon: "heart",
            title: "心率数据",
            description: "静息心率和实时心率监测",
            color: .red
        ),
        PermissionItem(
            icon: "bed.double.fill",
            title: "睡眠分析",
            description: "睡眠质量对压力水平的影响",
            color: .purple
        ),
        PermissionItem(
            icon: "figure.run",
            title: "运动数据",
            description: "运动对压力缓解的效果追踪",
            color: .green
        )
    ]
    
    var body: some View {
        NavigationView {
            ZStack {
                StressColors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        headerSection
                        permissionsSection
                        privacySection
                        actionSection
                    }
                    .padding()
                }
            }
            .navigationTitle("健康数据权限")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("跳过") {
                        dismiss()
                    }
                    .foregroundColor(StressColors.textSecondary)
                }
            }
        }
        .alert("权限被拒绝", isPresented: $showingPermissionAlert) {
            Button("去设置") {
                openSettings()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("请在设置中开启HealthKit权限以获得最佳体验")
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "heart.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(StressColors.primary)
            
            Text("健康数据访问")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(StressColors.textPrimary)
            
            Text("StressBalanz需要访问您的健康数据来提供个性化的压力管理建议")
                .font(.body)
                .foregroundColor(StressColors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var permissionsSection: some View {
        VStack(spacing: 16) {
            Text("我们需要访问以下数据")
                .font(.headline)
                .foregroundColor(StressColors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVStack(spacing: 12) {
                ForEach(permissionItems, id: \.title) { item in
                    PermissionRow(item: item)
                }
            }
        }
    }
    
    private var privacySection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "lock.shield.fill")
                    .foregroundColor(StressColors.primary)
                Text("隐私保护")
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 8) {
                PrivacyPoint(text: "所有数据仅在您的设备上处理")
                PrivacyPoint(text: "我们不会上传任何健康数据到服务器")
                PrivacyPoint(text: "您可以随时在设置中撤销权限")
            }
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(16)
    }
    
    private var actionSection: some View {
        VStack(spacing: 16) {
            PrimaryButton(
                title: "授权HealthKit访问",
                isLoading: isRequestingPermission
            ) {
                requestPermission()
            }
            
            Text("点击上方按钮将跳转到HealthKit授权页面")
                .font(.caption)
                .foregroundColor(StressColors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private func requestPermission() {
        isRequestingPermission = true
        
        Task {
            let success = await healthKitManager.requestHealthKitPermission()
            
            await MainActor.run {
                isRequestingPermission = false
                
                if success {
                    onPermissionGranted()
                    dismiss()
                } else {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func openSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

struct PermissionItem {
    let icon: String
    let title: String
    let description: String
    let color: Color
}

struct PermissionRow: View {
    let item: PermissionItem
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: item.icon)
                .font(.title2)
                .foregroundColor(item.color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(item.title)
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                
                Text(item.description)
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
            }
            
            Spacer()
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(12)
    }
}

struct PrivacyPoint: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(StressColors.lowStress)
                .font(.caption)
            
            Text(text)
                .font(.caption)
                .foregroundColor(StressColors.textSecondary)
            
            Spacer()
        }
    }
}

#Preview {
    HealthKitPermissionView {
        print("Permission granted")
    }
}