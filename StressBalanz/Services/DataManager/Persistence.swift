//
//  Persistence.swift
//  StressBalanz
//
//  Created by 刘青 on 2025/7/12.
//

import CoreData

struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext
        
        // Create sample user profile
        let userProfile = UserProfile(context: viewContext)
        userProfile.id = UUID()
        userProfile.createdAt = Date()
        userProfile.age = 30
        userProfile.gender = "male"
        userProfile.baselineHRV = 45.0
        userProfile.baselineHeartRate = 68.0
        userProfile.isActive = true
        
        // Create sample stress readings
        for i in 0..<10 {
            let stressReading = StressReading(context: viewContext)
            stressReading.timestamp = Date().addingTimeInterval(-Double(i * 3600))
            stressReading.stressLevel = Double.random(in: 0.1...0.9)
            stressReading.hrv = Double.random(in: 30...60)
            stressReading.heartRate = Double.random(in: 60...100)
            stressReading.confidence = Double.random(in: 0.7...1.0)
            stressReading.dataSource = "HealthKit"
            stressReading.user = userProfile
        }
        
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "StressBalanz")
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }
        
        // Configure Core Data for performance
        container.persistentStoreDescriptions.forEach { storeDescription in
            storeDescription.shouldMigrateStoreAutomatically = true
            storeDescription.shouldInferMappingModelAutomatically = true
        }
        
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
        container.viewContext.automaticallyMergesChangesFromParent = true
    }
}
