import SwiftUI

struct StressProgressView: View {
    let progress: Double // 0.0 to 1.0
    let title: String
    let subtitle: String?
    
    private var progressColor: Color {
        switch progress {
        case 0.0..<0.33:
            return StressColors.lowStress
        case 0.33..<0.66:
            return StressColors.mediumStress
        default:
            return StressColors.highStress
        }
    }
    
    private var progressPercentage: Int {
        Int(progress * 100)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.headline)
                    .foregroundColor(StressColors.textPrimary)
                Spacer()
                Text("\(progressPercentage)%")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(progressColor)
            }
            
            if let subtitle = subtitle {
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(StressColors.textSecondary)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: progressColor))
                .scaleEffect(x: 1, y: 2, anchor: .center)
        }
        .padding()
        .background(StressColors.surface)
        .cornerRadius(12)
    }
}

#Preview {
    VStack(spacing: 16) {
        StressProgressView(
            progress: 0.3,
            title: "今日压力水平",
            subtitle: "相比昨天降低了15%"
        )
        
        StressProgressView(
            progress: 0.7,
            title: "HRV趋势",
            subtitle: "心率变异性正在改善"
        )
        
        StressProgressView(
            progress: 0.9,
            title: "压力警告",
            subtitle: "建议立即进行放松练习"
        )
    }
    .padding()
    .background(StressColors.background)
}