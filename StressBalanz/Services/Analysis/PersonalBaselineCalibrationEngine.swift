import Foundation
import CoreData

/**
 PersonalBaselineCalibrationEngine
 
 个体基线校准机制
 根据用户的历史数据建立个人化的健康基线，
 提供个性化的压力评估和建议
 */
class PersonalBaselineCalibrationEngine {
    
    // MARK: - Configuration
    
    struct CalibrationConfig {
        static let minimumCalibrationDays: Int = 7
        static let optimalCalibrationDays: Int = 14
        static let maxCalibrationDays: Int = 30
        static let minimumDataPointsPerDay: Int = 3
        static let confidenceThreshold: Double = 0.7
        static let outlierPercentile: Double = 0.05 // 5%异常值剔除
        static let updateInterval: TimeInterval = 24 * 60 * 60 // 24小时
        static let seasonalAdjustmentPeriod: TimeInterval = 7 * 24 * 60 * 60 // 7天
    }
    
    // MARK: - Baseline Models
    
    struct PersonalBaseline {
        let userID: UUID
        let establishedDate: Date
        let lastUpdated: Date
        
        // 生理指标基线
        let hrvBaseline: HRVBaseline
        let heartRateBaseline: HeartRateBaseline
        let sleepBaseline: SleepBaseline
        
        // 环境和行为基线
        let circadianPattern: CircadianPattern
        let stressResponsePattern: StressResponsePattern
        let recoveryPattern: RecoveryPattern
        
        // 统计信息
        let dataQuality: BaselineQuality
        let confidence: Double
        let sampleSize: Int
        
        var isReliable: Bool {
            return confidence >= CalibrationConfig.confidenceThreshold &&
                   sampleSize >= CalibrationConfig.minimumCalibrationDays * CalibrationConfig.minimumDataPointsPerDay
        }
        
        var needsUpdate: Bool {
            return Date().timeIntervalSince(lastUpdated) > CalibrationConfig.updateInterval
        }
    }
    
    struct HRVBaseline {
        let meanRMSSD: Double
        let meanSDNN: Double
        let standardDeviation: Double
        let morningAverage: Double
        let eveningAverage: Double
        let weekdayAverage: Double
        let weekendAverage: Double
        let trends: [TimeInterval: Double] // 时间段 -> 平均值
        
        func getExpectedValue(for time: Date) -> Double {
            let hour = Calendar.current.component(.hour, from: time)
            let isWeekend = Calendar.current.isDateInWeekend(time)
            
            // 基于时间和星期调整
            var expectedValue = isWeekend ? weekendAverage : weekdayAverage
            
            // 昼夜节律调整
            if hour >= 6 && hour <= 10 {
                expectedValue = morningAverage
            } else if hour >= 18 && hour <= 22 {
                expectedValue = eveningAverage
            }
            
            return expectedValue
        }
        
        func calculateDeviation(_ currentValue: Double, for time: Date) -> Double {
            let expected = getExpectedValue(for: time)
            return abs(currentValue - expected) / standardDeviation
        }
    }
    
    struct HeartRateBaseline {
        let restingHeartRate: Double
        let averageHeartRate: Double
        let standardDeviation: Double
        let exerciseThreshold: Double
        let stressThreshold: Double
        let recoveryHeartRate: Double
        let ageAdjustedMax: Double
        
        func calculateStressIndicator(_ currentHR: Double) -> Double {
            if currentHR > stressThreshold {
                return min(1.0, (currentHR - restingHeartRate) / (stressThreshold - restingHeartRate))
            } else {
                return max(0.0, (currentHR - restingHeartRate) / (averageHeartRate - restingHeartRate))
            }
        }
    }
    
    struct SleepBaseline {
        let averageBedtime: Date
        let averageWakeTime: Date
        let optimalSleepDuration: TimeInterval
        let deepSleepPercentage: Double
        let remSleepPercentage: Double
        let sleepEfficiency: Double
        let weekdayPattern: SleepPattern
        let weekendPattern: SleepPattern
        
        struct SleepPattern {
            let bedtime: Date
            let wakeTime: Date
            let duration: TimeInterval
            let quality: Double
        }
        
        func calculateSleepDebt(_ actualSleep: TimeInterval) -> TimeInterval {
            return max(0, optimalSleepDuration - actualSleep)
        }
    }
    
    struct CircadianPattern {
        let chronotype: Chronotype // 昼夜节律类型
        let energyPeaks: [TimeInterval] // 一天中的能量高峰时间
        let stressPeaks: [TimeInterval] // 压力高峰时间
        let optimalBedtime: Date
        let naturalWakeTime: Date
        
        enum Chronotype {
            case earlyBird // 早睡早起
            case nightOwl  // 晚睡晚起
            case neutral   // 中间型
        }
        
        func getExpectedStressLevel(at time: Date) -> Double {
            let hour = Calendar.current.component(.hour, from: time)
            let timeInterval = TimeInterval(hour * 3600)
            
            // 查找最近的压力峰值
            let closestPeak = stressPeaks.min { abs($0 - timeInterval) < abs($1 - timeInterval) }
            
            if let peak = closestPeak {
                let distance = abs(timeInterval - peak)
                return max(0.2, 1.0 - distance / (4 * 3600)) // 4小时内的影响
            }
            
            return 0.5 // 默认中等水平
        }
    }
    
    struct StressResponsePattern {
        let baselineStressLevel: Double
        let stressReactivity: Double // 对压力的反应强度
        let adaptationRate: Double   // 适应速度
        let resilience: Double       // 恢复能力
        let triggerThresholds: [StressTrigger: Double]
        
        enum StressTrigger {
            case workload
            case social
            case physical
            case environmental
        }
        
        func predictStressResponse(trigger: StressTrigger, intensity: Double) -> Double {
            let threshold = triggerThresholds[trigger] ?? 0.5
            if intensity > threshold {
                return min(1.0, baselineStressLevel + (intensity - threshold) * stressReactivity)
            }
            return baselineStressLevel
        }
    }
    
    struct RecoveryPattern {
        let averageRecoveryTime: TimeInterval
        let hrvRecoveryRate: Double
        let heartRateRecoveryRate: Double
        let sleepRecoveryEfficiency: Double
        let activeRecoveryPreference: RecoveryType
        
        enum RecoveryType {
            case passive    // 休息
            case active     // 轻度活动
            case mixed      // 混合
        }
        
        func estimateRecoveryTime(stressLevel: Double) -> TimeInterval {
            return averageRecoveryTime * (1.0 + stressLevel * 0.5)
        }
    }
    
    struct BaselineQuality {
        let dataCompleteness: Double
        let temporalCoverage: Double
        let consistencyScore: Double
        let outlierPercentage: Double
        let seasonalStability: Double
        
        var overallQuality: Double {
            return (dataCompleteness + temporalCoverage + consistencyScore + 
                   (1.0 - outlierPercentage) + seasonalStability) / 5.0
        }
    }
    
    // MARK: - Core Data Context
    
    private let context: NSManagedObjectContext
    private var currentBaseline: PersonalBaseline?
    
    // Public accessor for Core Data context
    var managedObjectContext: NSManagedObjectContext {
        return context
    }
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - Baseline Establishment
    
    func establishBaseline(for userID: UUID) async -> PersonalBaseline? {
        print("开始建立用户 \(userID) 的个人基线...")
        
        // 1. 收集历史数据
        let historicalData = await collectHistoricalData(for: userID)
        
        guard validateDataSufficiency(historicalData) else {
            print("数据不足，无法建立可靠基线")
            return nil
        }
        
        // 2. 数据预处理
        let processedData = preprocessHistoricalData(historicalData)
        
        // 3. 建立各指标基线
        let hrvBaseline = await establishHRVBaseline(from: processedData.hrvData)
        let heartRateBaseline = await establishHeartRateBaseline(from: processedData.heartRateData)
        let sleepBaseline = await establishSleepBaseline(from: processedData.sleepData)
        
        // 4. 分析行为模式
        let circadianPattern = analyzeCircadianPattern(from: processedData)
        let stressPattern = analyzeStressResponsePattern(from: processedData)
        let recoveryPattern = analyzeRecoveryPattern(from: processedData)
        
        // 5. 计算质量指标
        let dataQuality = calculateBaselineQuality(from: processedData)
        let daysCovered = historicalData.endDate.timeIntervalSince(historicalData.startDate) / (24 * 60 * 60)
        let confidence = calculateOverallConfidence(
            dataQuality: dataQuality,
            dataPoints: processedData.totalDataPoints,
            daysCovered: daysCovered
        )
        
        // 6. 创建基线对象
        let baseline = PersonalBaseline(
            userID: userID,
            establishedDate: Date(),
            lastUpdated: Date(),
            hrvBaseline: hrvBaseline,
            heartRateBaseline: heartRateBaseline,
            sleepBaseline: sleepBaseline,
            circadianPattern: circadianPattern,
            stressResponsePattern: stressPattern,
            recoveryPattern: recoveryPattern,
            dataQuality: dataQuality,
            confidence: confidence,
            sampleSize: processedData.totalDataPoints
        )
        
        // 7. 保存基线
        await saveBaseline(baseline)
        currentBaseline = baseline
        
        print("个人基线建立完成，置信度: \(String(format: "%.2f", confidence))")
        return baseline
    }
    
    func updateBaseline(for userID: UUID) async -> PersonalBaseline? {
        guard let existingBaseline = await loadBaseline(for: userID) else {
            return await establishBaseline(for: userID)
        }
        
        // 检查是否需要更新
        guard existingBaseline.needsUpdate else {
            return existingBaseline
        }
        
        print("更新用户 \(userID) 的个人基线...")
        
        // 收集自上次更新以来的新数据
        let newData = await collectRecentData(for: userID, since: existingBaseline.lastUpdated)
        
        // 渐进式更新基线
        let updatedBaseline = await performIncrementalUpdate(
            baseline: existingBaseline,
            newData: newData
        )
        
        await saveBaseline(updatedBaseline)
        currentBaseline = updatedBaseline
        
        return updatedBaseline
    }
    
    // MARK: - Data Collection
    
    private func collectHistoricalData(for userID: UUID) async -> HistoricalHealthData {
        let endDate = Date()
        let startDate = Calendar.current.date(
            byAdding: .day,
            value: -CalibrationConfig.maxCalibrationDays,
            to: endDate
        ) ?? endDate
        
        // 从Core Data获取历史数据
        let hrvData = await fetchHRVData(for: userID, from: startDate, to: endDate)
        let heartRateData = await fetchHeartRateData(for: userID, from: startDate, to: endDate)
        let sleepData = await fetchSleepData(for: userID, from: startDate, to: endDate)
        let stressReadings = await fetchStressReadings(for: userID, from: startDate, to: endDate)
        
        return HistoricalHealthData(
            hrvData: hrvData,
            heartRateData: heartRateData,
            sleepData: sleepData,
            stressReadings: stressReadings,
            startDate: startDate,
            endDate: endDate
        )
    }
    
    private func collectRecentData(for userID: UUID, since: Date) async -> HistoricalHealthData {
        let endDate = Date()
        
        let hrvData = await fetchHRVData(for: userID, from: since, to: endDate)
        let heartRateData = await fetchHeartRateData(for: userID, from: since, to: endDate)
        let sleepData = await fetchSleepData(for: userID, from: since, to: endDate)
        let stressReadings = await fetchStressReadings(for: userID, from: since, to: endDate)
        
        return HistoricalHealthData(
            hrvData: hrvData,
            heartRateData: heartRateData,
            sleepData: sleepData,
            stressReadings: stressReadings,
            startDate: since,
            endDate: endDate
        )
    }
    
    // MARK: - Data Fetching Methods
    
    private func fetchHRVData(for userID: UUID, from startDate: Date, to endDate: Date) async -> [TimestampedValue] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
                request.predicate = NSPredicate(
                    format: "dataType == %@ AND timestamp >= %@ AND timestamp <= %@",
                    "HRV", startDate as NSDate, endDate as NSDate
                )
                request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
                
                do {
                    let results = try self.context.fetch(request)
                    let data = results.map { TimestampedValue(timestamp: $0.timestamp!, value: $0.value) }
                    continuation.resume(returning: data)
                } catch {
                    print("获取HRV数据失败: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    private func fetchHeartRateData(for userID: UUID, from startDate: Date, to endDate: Date) async -> [TimestampedValue] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
                request.predicate = NSPredicate(
                    format: "dataType == %@ AND timestamp >= %@ AND timestamp <= %@",
                    "HeartRate", startDate as NSDate, endDate as NSDate
                )
                request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
                
                do {
                    let results = try self.context.fetch(request)
                    let data = results.map { TimestampedValue(timestamp: $0.timestamp!, value: $0.value) }
                    continuation.resume(returning: data)
                } catch {
                    print("获取心率数据失败: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    private func fetchSleepData(for userID: UUID, from startDate: Date, to endDate: Date) async -> [SleepDataPoint] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
                request.predicate = NSPredicate(
                    format: "dataType == %@ AND timestamp >= %@ AND timestamp <= %@",
                    "Sleep", startDate as NSDate, endDate as NSDate
                )
                request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
                
                do {
                    let results = try self.context.fetch(request)
                    let data = results.compactMap { healthKitData -> SleepDataPoint? in
                        guard let metadata = healthKitData.metadata as? [String: Any],
                              let startDate = metadata["startDate"] as? Date,
                              let endDate = metadata["endDate"] as? Date,
                              let duration = metadata["duration"] as? TimeInterval else {
                            return nil
                        }
                        
                        return SleepDataPoint(
                            date: healthKitData.timestamp!,
                            bedtime: startDate,
                            wakeTime: endDate,
                            duration: duration,
                            quality: healthKitData.value / 100.0, // 假设存储为百分比
                            efficiency: 0.85 // 默认值，实际应从数据计算
                        )
                    }
                    continuation.resume(returning: data)
                } catch {
                    print("获取睡眠数据失败: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    private func fetchStressReadings(for userID: UUID, from startDate: Date, to endDate: Date) async -> [StressDataPoint] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<StressReading> = StressReading.fetchRequest()
                request.predicate = NSPredicate(
                    format: "timestamp >= %@ AND timestamp <= %@",
                    startDate as NSDate, endDate as NSDate
                )
                request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
                
                do {
                    let results = try self.context.fetch(request)
                    let data = results.map { reading in
                        StressDataPoint(
                            timestamp: reading.timestamp!,
                            stressLevel: reading.stressLevel,
                            confidence: reading.confidence,
                            hrv: reading.hrv,
                            heartRate: reading.heartRate
                        )
                    }
                    continuation.resume(returning: data)
                } catch {
                    print("获取压力数据失败: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
}

// MARK: - Supporting Data Structures

struct HistoricalHealthData {
    let hrvData: [TimestampedValue]
    let heartRateData: [TimestampedValue]
    let sleepData: [SleepDataPoint]
    let stressReadings: [StressDataPoint]
    let startDate: Date
    let endDate: Date
    
    var totalDataPoints: Int {
        return hrvData.count + heartRateData.count + sleepData.count + stressReadings.count
    }
}

struct ProcessedHealthData {
    let hrvData: [TimestampedValue]
    let heartRateData: [TimestampedValue]
    let sleepData: [SleepDataPoint]
    let stressReadings: [StressDataPoint]
    let totalDataPoints: Int
    let dataQuality: Double
}

struct TimestampedValue {
    let timestamp: Date
    let value: Double
}

struct SleepDataPoint {
    let date: Date
    let bedtime: Date
    let wakeTime: Date
    let duration: TimeInterval
    let quality: Double
    let efficiency: Double
}

struct StressDataPoint {
    let timestamp: Date
    let stressLevel: Double
    let confidence: Double
    let hrv: Double?
    let heartRate: Double?
}