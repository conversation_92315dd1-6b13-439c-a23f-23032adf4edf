import Foundation
import CoreData
import SwiftUI

/**
 StressAnalysisService Extensions
 
 压力分析服务的扩展功能
 */
extension StressAnalysisService {
    
    // MARK: - Setup Methods
    
    func setupNotificationObservers() {
        // 监听HealthKit数据更新
        NotificationCenter.default.publisher(for: .newHRVDataReceived)
            .sink { [weak self] _ in
                Task {
                    await self?.performAutomaticAnalysis()
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: .newHeartRateDataReceived)
            .sink { [weak self] _ in
                Task {
                    await self?.performAutomaticAnalysis()
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: .newSleepDataReceived)
            .sink { [weak self] _ in
                Task {
                    await self?.performAutomaticAnalysis()
                }
            }
            .store(in: &cancellables)
        
        // 监听数据验证完成
        NotificationCenter.default.publisher(for: .dataValidationCompleted)
            .sink { [weak self] notification in
                if let validationReport = notification.object as? ValidationReport {
                    print("数据验证完成，质量等级: \(validationReport.qualityLevel)")
                }
            }
            .store(in: &cancellables)
    }
    
    func setupAnalysisTimer() {
        // 在初始化时不启动timer，等待用户授权后再启动
    }
    
    // MARK: - Personal Baseline Management
    
    func loadPersonalBaseline() async {
        guard let userProfile = await getCurrentUserProfile() else {
            print("未找到用户配置文件，跳过基线加载")
            return
        }
        
        let baseline = await baselineEngine.loadBaseline(for: userProfile.id!)
        
        await MainActor.run {
            personalBaseline = baseline
            
            if baseline != nil {
                print("个人基线加载成功")
            } else {
                print("未找到个人基线，将在数据充足时建立")
            }
        }
    }
    
    func getCurrentUserProfile() async -> UserProfile? {
        return await withCheckedContinuation { continuation in
            baselineEngine.managedObjectContext.perform {
                let request: NSFetchRequest<UserProfile> = UserProfile.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
                request.fetchLimit = 1
                
                do {
                    let results = try self.baselineEngine.managedObjectContext.fetch(request)
                    continuation.resume(returning: results.first)
                } catch {
                    print("获取用户配置文件失败: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    // MARK: - Emergency Handling
    
    func handleEmergencyStressLevel(_ record: AnalysisRecord) async {
        print("⚠️ 检测到紧急压力水平: \(record.stressLevel)")
        
        // 发送紧急通知
        NotificationCenter.default.post(
            name: .emergencyStressDetected,
            object: record
        )
        
        // 可以在这里添加额外的紧急处理逻辑
        // 例如：推送通知、紧急建议等
        
        await MainActor.run {
            recommendations.insert("压力水平非常高，建议立即休息并考虑寻求专业帮助", at: 0)
        }
    }
    
    // MARK: - Analysis History Management
    
    func getAnalysisHistory(days: Int = 7) -> [AnalysisRecord] {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        return analysisHistory.filter { $0.timestamp >= cutoffDate }
    }
    
    func getStressStatistics(for period: StatisticsPeriod) -> StressStatistics {
        let records = getRecordsForPeriod(period)
        
        guard !records.isEmpty else {
            return StressStatistics(
                averageLevel: 0.0,
                minimumLevel: 0.0,
                maximumLevel: 0.0,
                standardDeviation: 0.0,
                trend: .stable,
                qualityScore: 0.0,
                dataPoints: 0
            )
        }
        
        let levels = records.map { $0.stressLevel }
        let average = levels.reduce(0, +) / Double(levels.count)
        let minimum = levels.min() ?? 0.0
        let maximum = levels.max() ?? 0.0
        
        // 计算标准差
        let variance = levels.map { pow($0 - average, 2) }.reduce(0, +) / Double(levels.count)
        let standardDeviation = sqrt(variance)
        
        // 计算趋势
        let trend = calculateStressTrend(from: records)
        
        // 计算质量评分
        let reliableRecords = records.filter { $0.isReliable }
        let qualityScore = Double(reliableRecords.count) / Double(records.count)
        
        return StressStatistics(
            averageLevel: average,
            minimumLevel: minimum,
            maximumLevel: maximum,
            standardDeviation: standardDeviation,
            trend: trend,
            qualityScore: qualityScore,
            dataPoints: records.count
        )
    }
    
    private func getRecordsForPeriod(_ period: StatisticsPeriod) -> [AnalysisRecord] {
        let calendar = Calendar.current
        let now = Date()
        
        let startDate: Date
        switch period {
        case .today:
            startDate = calendar.startOfDay(for: now)
        case .week:
            startDate = calendar.date(byAdding: .day, value: -7, to: now) ?? now
        case .month:
            startDate = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        case .quarter:
            startDate = calendar.date(byAdding: .month, value: -3, to: now) ?? now
        }
        
        return analysisHistory.filter { $0.timestamp >= startDate }
    }
    
    // MARK: - Recommendations Engine
    
    func generatePersonalizedRecommendations() -> [PersonalizedRecommendation] {
        guard let baseline = personalBaseline else {
            return getDefaultRecommendations()
        }
        
        var recommendations: [PersonalizedRecommendation] = []
        
        // 基于当前压力水平的建议
        recommendations.append(contentsOf: generateStressLevelRecommendations())
        
        // 基于个人模式的建议
        recommendations.append(contentsOf: generatePatternBasedRecommendations(baseline))
        
        // 基于历史趋势的建议
        recommendations.append(contentsOf: generateTrendBasedRecommendations())
        
        // 基于时间的建议
        recommendations.append(contentsOf: generateTimeBasedRecommendations(baseline))
        
        return recommendations
    }
    
    private func generateStressLevelRecommendations() -> [PersonalizedRecommendation] {
        var recommendations: [PersonalizedRecommendation] = []
        
        switch stressClassification {
        case .veryLow, .low:
            recommendations.append(PersonalizedRecommendation(
                type: .maintenance,
                title: "保持当前状态",
                description: "您的压力水平很好，继续保持规律的作息和健康习惯",
                priority: .low,
                estimatedDuration: 0
            ))
            
        case .moderate:
            recommendations.append(PersonalizedRecommendation(
                type: .breathing,
                title: "深呼吸练习",
                description: "进行5分钟的深呼吸练习，有助于降低中等压力水平",
                priority: .medium,
                estimatedDuration: 300
            ))
            
        case .high:
            recommendations.append(PersonalizedRecommendation(
                type: .mindfulness,
                title: "正念冥想",
                description: "建议进行10-15分钟的正念冥想来缓解高压力",
                priority: .high,
                estimatedDuration: 900
            ))
            
        case .veryHigh:
            recommendations.append(PersonalizedRecommendation(
                type: .emergency,
                title: "立即休息",
                description: "压力水平很高，请立即停下手头工作，寻找安静地方休息",
                priority: .urgent,
                estimatedDuration: 1800
            ))
        }
        
        return recommendations
    }
    
    private func generatePatternBasedRecommendations(_ baseline: PersonalBaselineCalibrationEngine.PersonalBaseline) -> [PersonalizedRecommendation] {
        var recommendations: [PersonalizedRecommendation] = []
        
        // 基于昼夜节律的建议
        let now = Date()
        let currentHour = Calendar.current.component(.hour, from: now)
        let expectedStress = baseline.circadianPattern.getExpectedStressLevel(at: now)
        
        if currentStressLevel > expectedStress + 0.2 {
            recommendations.append(PersonalizedRecommendation(
                type: .schedule,
                title: "调整活动安排",
                description: "当前时段您的压力水平高于个人正常模式，建议调整活动强度",
                priority: .medium,
                estimatedDuration: 0
            ))
        }
        
        // 基于睡眠模式的建议
        let optimalBedtime = baseline.sleepBaseline.averageBedtime
        let optimalBedtimeHour = Calendar.current.component(.hour, from: optimalBedtime)
        
        if currentHour >= 21 && currentHour <= optimalBedtimeHour + 1 {
            recommendations.append(PersonalizedRecommendation(
                type: .sleep,
                title: "准备就寝",
                description: "接近您的最佳就寝时间，开始准备睡前例行程序",
                priority: .medium,
                estimatedDuration: 1800
            ))
        }
        
        return recommendations
    }
    
    private func generateTrendBasedRecommendations() -> [PersonalizedRecommendation] {
        var recommendations: [PersonalizedRecommendation] = []
        
        let recentRecords = Array(analysisHistory.prefix(10))
        let trend = calculateStressTrend(from: recentRecords)
        
        switch trend {
        case .increasing:
            recommendations.append(PersonalizedRecommendation(
                type: .intervention,
                title: "压力上升趋势",
                description: "您的压力水平呈上升趋势，建议主动采取减压措施",
                priority: .high,
                estimatedDuration: 600
            ))
            
        case .decreasing:
            recommendations.append(PersonalizedRecommendation(
                type: .maintenance,
                title: "压力下降良好",
                description: "您的压力水平在改善，继续保持当前的减压方法",
                priority: .low,
                estimatedDuration: 0
            ))
            
        case .stable:
            if currentStressLevel > 0.6 {
                recommendations.append(PersonalizedRecommendation(
                    type: .lifestyle,
                    title: "持续高压状态",
                    description: "压力水平持续较高，建议评估生活方式并寻求长期解决方案",
                    priority: .medium,
                    estimatedDuration: 0
                ))
            }
        }
        
        return recommendations
    }
    
    private func generateTimeBasedRecommendations(_ baseline: PersonalBaselineCalibrationEngine.PersonalBaseline) -> [PersonalizedRecommendation] {
        var recommendations: [PersonalizedRecommendation] = []
        
        let now = Date()
        let hour = Calendar.current.component(.hour, from: now)
        
        // 根据时间段提供建议
        switch hour {
        case 6...9: // 早晨
            recommendations.append(PersonalizedRecommendation(
                type: .exercise,
                title: "晨间活动",
                description: "早晨进行轻度运动有助于设定一天的积极基调",
                priority: .low,
                estimatedDuration: 900
            ))
            
        case 12...14: // 午餐时间
            recommendations.append(PersonalizedRecommendation(
                type: .nutrition,
                title: "营养午餐",
                description: "均衡的午餐有助于维持下午的能量和情绪稳定",
                priority: .medium,
                estimatedDuration: 1800
            ))
            
        case 15...17: // 下午疲劳期
            if currentStressLevel > 0.5 {
                recommendations.append(PersonalizedRecommendation(
                    type: .break,
                    title: "下午小憩",
                    description: "下午是常见的疲劳时段，短暂休息可以恢复精力",
                    priority: .medium,
                    estimatedDuration: 300
                ))
            }
            
        case 18...20: // 晚餐时间
            recommendations.append(PersonalizedRecommendation(
                type: .social,
                title: "放松时光",
                description: "与家人朋友共度时光，有助于缓解一天的压力",
                priority: .medium,
                estimatedDuration: 3600
            ))
            
        default:
            break
        }
        
        return recommendations
    }
    
    private func getDefaultRecommendations() -> [PersonalizedRecommendation] {
        return [
            PersonalizedRecommendation(
                type: .breathing,
                title: "深呼吸练习",
                description: "进行简单的深呼吸练习，帮助即时缓解压力",
                priority: .medium,
                estimatedDuration: 300
            ),
            PersonalizedRecommendation(
                type: .mindfulness,
                title: "正念练习",
                description: "尝试短暂的正念练习，提高对当下的觉察",
                priority: .medium,
                estimatedDuration: 600
            )
        ]
    }
    
    // MARK: - Export and Reporting
    
    func exportAnalysisData(format: ExportFormat) async -> Data? {
        switch format {
        case .json:
            return await exportAsJSON()
        case .csv:
            return await exportAsCSV()
        case .pdf:
            return await exportAsPDF()
        }
    }
    
    private func exportAsJSON() async -> Data? {
        let exportData = ExportData(
            analysisHistory: analysisHistory,
            personalBaseline: personalBaseline,
            exportDate: Date(),
            version: "1.0"
        )
        
        do {
            return try JSONEncoder().encode(exportData)
        } catch {
            print("JSON导出失败: \(error)")
            return nil
        }
    }
    
    private func exportAsCSV() async -> Data? {
        var csvContent = "Timestamp,StressLevel,Classification,Confidence,DataQuality\n"
        
        for record in analysisHistory {
            let row = "\(record.timestamp),\(record.stressLevel),\(record.classification.description),\(record.confidence),\(record.dataQuality.rawValue)\n"
            csvContent += row
        }
        
        return csvContent.data(using: .utf8)
    }
    
    private func exportAsPDF() async -> Data? {
        // PDF导出的简化实现
        // 实际应用中需要使用PDFKit或其他PDF生成库
        print("PDF导出功能需要进一步实现")
        return nil
    }
}

// MARK: - Supporting Enums and Structures

enum StatisticsPeriod {
    case today
    case week
    case month
    case quarter
}

struct StressStatistics {
    let averageLevel: Double
    let minimumLevel: Double
    let maximumLevel: Double
    let standardDeviation: Double
    let trend: StressTrend
    let qualityScore: Double
    let dataPoints: Int
}

struct PersonalizedRecommendation {
    let type: RecommendationType
    let title: String
    let description: String
    let priority: Priority
    let estimatedDuration: TimeInterval // 秒
    
    enum RecommendationType {
        case breathing
        case mindfulness
        case exercise
        case nutrition
        case sleep
        case social
        case `break`
        case lifestyle
        case schedule
        case intervention
        case maintenance
        case emergency
    }
    
    enum Priority {
        case low
        case medium
        case high
        case urgent
        
        var color: Color {
            switch self {
            case .low: return .blue
            case .medium: return .yellow
            case .high: return .orange
            case .urgent: return .red
            }
        }
    }
}

enum ExportFormat {
    case json
    case csv
    case pdf
}

struct ExportData: Codable {
    let analysisHistory: [ExportAnalysisRecord]
    let personalBaseline: ExportPersonalBaseline?
    let exportDate: Date
    let version: String
    
    init(analysisHistory: [StressAnalysisService.AnalysisRecord], personalBaseline: PersonalBaselineCalibrationEngine.PersonalBaseline?, exportDate: Date, version: String) {
        self.analysisHistory = analysisHistory.map { ExportAnalysisRecord(from: $0) }
        self.personalBaseline = personalBaseline.map { ExportPersonalBaseline(from: $0) }
        self.exportDate = exportDate
        self.version = version
    }
}

struct ExportAnalysisRecord: Codable {
    let timestamp: Date
    let stressLevel: Double
    let classification: String
    let confidence: Double
    let dataQuality: String
    
    init(from record: StressAnalysisService.AnalysisRecord) {
        self.timestamp = record.timestamp
        self.stressLevel = record.stressLevel
        self.classification = record.classification.description
        self.confidence = record.confidence
        self.dataQuality = record.dataQuality.rawValue
    }
}

struct ExportPersonalBaseline: Codable {
    let establishedDate: Date
    let confidence: Double
    let meanHRV: Double
    let restingHeartRate: Double
    
    init(from baseline: PersonalBaselineCalibrationEngine.PersonalBaseline) {
        self.establishedDate = baseline.establishedDate
        self.confidence = baseline.confidence
        self.meanHRV = baseline.hrvBaseline.meanRMSSD
        self.restingHeartRate = baseline.heartRateBaseline.restingHeartRate
    }
}