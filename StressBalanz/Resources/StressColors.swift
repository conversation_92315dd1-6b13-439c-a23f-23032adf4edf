import SwiftUI

struct StressColors {
    static let primary = Color(red: 0.1, green: 0.7, blue: 0.9)
    static let secondary = Color(red: 0.9, green: 0.5, blue: 0.1)
    static let background = Color(red: 0.05, green: 0.05, blue: 0.1)
    static let surface = Color(red: 0.1, green: 0.1, blue: 0.15)
    
    static let lowStress = Color(red: 0.2, green: 0.8, blue: 0.3)
    static let mediumStress = Color(red: 0.9, green: 0.7, blue: 0.1)
    static let highStress = Color(red: 0.9, green: 0.3, blue: 0.2)
    
    static let textPrimary = Color.white
    static let textSecondary = Color.gray
}