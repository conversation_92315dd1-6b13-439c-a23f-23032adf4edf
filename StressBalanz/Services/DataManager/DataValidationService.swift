import Foundation
import HealthKit

class DataValidationService {
    
    // MARK: - Validation Results
    
    struct ValidationResult {
        let isValid: Bool
        let confidence: Double // 0.0 - 1.0
        let issues: [ValidationIssue]
        let timestamp: Date
        
        var qualityScore: DataQuality {
            switch confidence {
            case 0.9...1.0:
                return .excellent
            case 0.7..<0.9:
                return .good
            case 0.5..<0.7:
                return .fair
            case 0.3..<0.5:
                return .poor
            default:
                return .invalid
            }
        }
    }
    
    struct ValidationIssue {
        let type: IssueType
        let severity: Severity
        let description: String
        let affectedMetric: String
        
        enum IssueType {
            case outlier
            case missing
            case inconsistent
            case unrealistic
            case deviceError
        }
        
        enum Severity {
            case low
            case medium
            case high
            case critical
        }
    }
    
    enum DataQuality: String, CaseIterable {
        case excellent = "优秀"
        case good = "良好"
        case fair = "一般"
        case poor = "较差"
        case invalid = "无效"
        
        var color: String {
            switch self {
            case .excellent:
                return "green"
            case .good:
                return "blue"
            case .fair:
                return "orange"
            case .poor:
                return "red"
            case .invalid:
                return "gray"
            }
        }
    }
    
    // MARK: - HRV Data Validation
    
    func validateHRVData(_ samples: [HKQuantitySample], userProfile: UserProfile? = nil) -> ValidationResult {
        var issues: [ValidationIssue] = []
        var totalConfidence: Double = 1.0
        
        guard !samples.isEmpty else {
            issues.append(ValidationIssue(
                type: .missing,
                severity: .critical,
                description: "没有HRV数据",
                affectedMetric: "HRV"
            ))
            return ValidationResult(isValid: false, confidence: 0.0, issues: issues, timestamp: Date())
        }
        
        // 1. 检查数据范围合理性
        let hrvValues = samples.map { $0.quantity.doubleValue(for: HKUnit.secondUnit(with: .milli)) }
        let validationResults = validateHRVRange(hrvValues)
        issues.append(contentsOf: validationResults.issues)
        totalConfidence *= validationResults.confidence
        
        // 2. 检查数据连续性
        let continuityResults = validateDataContinuity(samples)
        issues.append(contentsOf: continuityResults.issues)
        totalConfidence *= continuityResults.confidence
        
        // 3. 检查异常值
        let outlierResults = validateOutliers(hrvValues, metric: "HRV")
        issues.append(contentsOf: outlierResults.issues)
        totalConfidence *= outlierResults.confidence
        
        // 4. 检查个体基线一致性
        if let userProfile = userProfile {
            let baseline = userProfile.baselineHRV
            let baselineResults = validateAgainstBaseline(hrvValues, baseline: baseline, metric: "HRV", tolerance: 0.3)
            issues.append(contentsOf: baselineResults.issues)
            totalConfidence *= baselineResults.confidence
        }
        
        return ValidationResult(
            isValid: totalConfidence > 0.5,
            confidence: totalConfidence,
            issues: issues,
            timestamp: Date()
        )
    }
    
    // MARK: - Heart Rate Data Validation
    
    func validateHeartRateData(_ samples: [HKQuantitySample], userProfile: UserProfile? = nil) -> ValidationResult {
        var issues: [ValidationIssue] = []
        var totalConfidence: Double = 1.0
        
        guard !samples.isEmpty else {
            issues.append(ValidationIssue(
                type: .missing,
                severity: .critical,
                description: "没有心率数据",
                affectedMetric: "心率"
            ))
            return ValidationResult(isValid: false, confidence: 0.0, issues: issues, timestamp: Date())
        }
        
        let heartRateValues = samples.map { $0.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute())) }
        
        // 1. 检查心率范围合理性 (通常在30-220之间)
        let rangeResults = validateHeartRateRange(heartRateValues)
        issues.append(contentsOf: rangeResults.issues)
        totalConfidence *= rangeResults.confidence
        
        // 2. 检查数据连续性
        let continuityResults = validateDataContinuity(samples)
        issues.append(contentsOf: continuityResults.issues)
        totalConfidence *= continuityResults.confidence
        
        // 3. 检查异常值
        let outlierResults = validateOutliers(heartRateValues, metric: "心率")
        issues.append(contentsOf: outlierResults.issues)
        totalConfidence *= outlierResults.confidence
        
        // 4. 检查个体基线一致性
        if let userProfile = userProfile {
            let baseline = userProfile.baselineHeartRate
            let baselineResults = validateAgainstBaseline(heartRateValues, baseline: baseline, metric: "心率", tolerance: 0.25)
            issues.append(contentsOf: baselineResults.issues)
            totalConfidence *= baselineResults.confidence
        }
        
        return ValidationResult(
            isValid: totalConfidence > 0.5,
            confidence: totalConfidence,
            issues: issues,
            timestamp: Date()
        )
    }
    
    // MARK: - Sleep Data Validation
    
    func validateSleepData(_ sleepQuality: SleepQualityData) -> ValidationResult {
        var issues: [ValidationIssue] = []
        var totalConfidence: Double = 1.0
        
        // 1. 检查睡眠时间合理性
        let totalSleep = sleepQuality.totalSleepTime
        if totalSleep < 2.0 || totalSleep > 16.0 {
            issues.append(ValidationIssue(
                type: .unrealistic,
                severity: totalSleep < 1.0 || totalSleep > 20.0 ? .high : .medium,
                description: "睡眠时间异常: \(String(format: "%.1f", totalSleep))小时",
                affectedMetric: "睡眠时间"
            ))
            totalConfidence *= totalSleep < 1.0 || totalSleep > 20.0 ? 0.3 : 0.7
        }
        
        // 2. 检查睡眠效率
        if sleepQuality.sleepEfficiency > 100.0 || sleepQuality.sleepEfficiency < 0.0 {
            issues.append(ValidationIssue(
                type: .unrealistic,
                severity: .high,
                description: "睡眠效率数值异常: \(String(format: "%.1f", sleepQuality.sleepEfficiency))%",
                affectedMetric: "睡眠效率"
            ))
            totalConfidence *= 0.5
        }
        
        // 3. 检查深度睡眠比例
        if totalSleep > 0 {
            let deepSleepPercentage = (sleepQuality.deepSleep / totalSleep) * 100
            if deepSleepPercentage > 40.0 || deepSleepPercentage < 0.0 {
                issues.append(ValidationIssue(
                    type: .unrealistic,
                    severity: deepSleepPercentage > 50.0 ? .high : .medium,
                    description: "深度睡眠比例异常: \(String(format: "%.1f", deepSleepPercentage))%",
                    affectedMetric: "深度睡眠"
                ))
                totalConfidence *= deepSleepPercentage > 50.0 ? 0.5 : 0.8
            }
        }
        
        // 4. 检查REM睡眠比例
        if totalSleep > 0 {
            let remSleepPercentage = (sleepQuality.remSleep / totalSleep) * 100
            if remSleepPercentage > 35.0 || remSleepPercentage < 0.0 {
                issues.append(ValidationIssue(
                    type: .unrealistic,
                    severity: remSleepPercentage > 40.0 ? .high : .medium,
                    description: "REM睡眠比例异常: \(String(format: "%.1f", remSleepPercentage))%",
                    affectedMetric: "REM睡眠"
                ))
                totalConfidence *= remSleepPercentage > 40.0 ? 0.5 : 0.8
            }
        }
        
        return ValidationResult(
            isValid: totalConfidence > 0.5,
            confidence: totalConfidence,
            issues: issues,
            timestamp: Date()
        )
    }
    
    // MARK: - Private Validation Methods
    
    private func validateHRVRange(_ values: [Double]) -> (confidence: Double, issues: [ValidationIssue]) {
        var confidence: Double = 1.0
        var issues: [ValidationIssue] = []
        
        let validRange = 10.0...150.0 // HRV合理范围（毫秒）
        let invalidValues = values.filter { !validRange.contains($0) }
        
        if !invalidValues.isEmpty {
            let invalidRatio = Double(invalidValues.count) / Double(values.count)
            confidence *= max(0.2, 1.0 - invalidRatio * 2.0)
            
            issues.append(ValidationIssue(
                type: .unrealistic,
                severity: invalidRatio > 0.5 ? .high : .medium,
                description: "发现\(invalidValues.count)个HRV值超出合理范围",
                affectedMetric: "HRV"
            ))
        }
        
        return (confidence, issues)
    }
    
    private func validateHeartRateRange(_ values: [Double]) -> (confidence: Double, issues: [ValidationIssue]) {
        var confidence: Double = 1.0
        var issues: [ValidationIssue] = []
        
        let validRange = 30.0...220.0 // 心率合理范围（次/分钟）
        let invalidValues = values.filter { !validRange.contains($0) }
        
        if !invalidValues.isEmpty {
            let invalidRatio = Double(invalidValues.count) / Double(values.count)
            confidence *= max(0.2, 1.0 - invalidRatio * 2.0)
            
            issues.append(ValidationIssue(
                type: .unrealistic,
                severity: invalidRatio > 0.5 ? .high : .medium,
                description: "发现\(invalidValues.count)个心率值超出合理范围",
                affectedMetric: "心率"
            ))
        }
        
        return (confidence, issues)
    }
    
    private func validateDataContinuity(_ samples: [HKQuantitySample]) -> (confidence: Double, issues: [ValidationIssue]) {
        guard samples.count > 1 else {
            return (1.0, [])
        }
        
        var confidence: Double = 1.0
        var issues: [ValidationIssue] = []
        
        // 检查时间间隔是否过大
        let sortedSamples = samples.sorted { $0.endDate < $1.endDate }
        var largeGaps = 0
        
        for i in 1..<sortedSamples.count {
            let timeInterval = sortedSamples[i].startDate.timeIntervalSince(sortedSamples[i-1].endDate)
            if timeInterval > 3600 { // 超过1小时的间隔
                largeGaps += 1
            }
        }
        
        if largeGaps > 0 {
            let gapRatio = Double(largeGaps) / Double(samples.count - 1)
            confidence *= max(0.5, 1.0 - gapRatio)
            
            issues.append(ValidationIssue(
                type: .inconsistent,
                severity: gapRatio > 0.3 ? .medium : .low,
                description: "数据存在\(largeGaps)个较大时间间隔",
                affectedMetric: "时间连续性"
            ))
        }
        
        return (confidence, issues)
    }
    
    private func validateOutliers(_ values: [Double], metric: String) -> (confidence: Double, issues: [ValidationIssue]) {
        guard values.count > 2 else {
            return (1.0, [])
        }
        
        var confidence: Double = 1.0
        var issues: [ValidationIssue] = []
        
        // 使用IQR方法检测异常值
        let sortedValues = values.sorted()
        let q1Index = sortedValues.count / 4
        let q3Index = (sortedValues.count * 3) / 4
        
        guard q1Index < sortedValues.count && q3Index < sortedValues.count else {
            return (1.0, [])
        }
        
        let q1 = sortedValues[q1Index]
        let q3 = sortedValues[q3Index]
        let iqr = q3 - q1
        let lowerBound = q1 - 1.5 * iqr
        let upperBound = q3 + 1.5 * iqr
        
        let outliers = values.filter { $0 < lowerBound || $0 > upperBound }
        
        if !outliers.isEmpty {
            let outlierRatio = Double(outliers.count) / Double(values.count)
            confidence *= max(0.3, 1.0 - outlierRatio * 1.5)
            
            issues.append(ValidationIssue(
                type: .outlier,
                severity: outlierRatio > 0.2 ? .medium : .low,
                description: "发现\(outliers.count)个\(metric)异常值",
                affectedMetric: metric
            ))
        }
        
        return (confidence, issues)
    }
    
    private func validateAgainstBaseline(_ values: [Double], baseline: Double, metric: String, tolerance: Double) -> (confidence: Double, issues: [ValidationIssue]) {
        var confidence: Double = 1.0
        var issues: [ValidationIssue] = []
        
        let average = values.reduce(0, +) / Double(values.count)
        let deviation = abs(average - baseline) / baseline
        
        if deviation > tolerance {
            confidence *= max(0.5, 1.0 - (deviation - tolerance))
            
            issues.append(ValidationIssue(
                type: .inconsistent,
                severity: deviation > tolerance * 2 ? .medium : .low,
                description: "\(metric)平均值偏离基线\(String(format: "%.1f", deviation * 100))%",
                affectedMetric: metric
            ))
        }
        
        return (confidence, issues)
    }
}

// MARK: - Validation Extensions

extension DataValidationService {
    
    func generateValidationReport(for results: [ValidationResult]) -> ValidationReport {
        let allIssues = results.flatMap { $0.issues }
        let averageConfidence = results.map { $0.confidence }.reduce(0, +) / Double(results.count)
        
        let criticalIssues = allIssues.filter { $0.severity == .critical }
        let highIssues = allIssues.filter { $0.severity == .high }
        let mediumIssues = allIssues.filter { $0.severity == .medium }
        let lowIssues = allIssues.filter { $0.severity == .low }
        
        return ValidationReport(
            overallConfidence: averageConfidence,
            totalIssues: allIssues.count,
            criticalIssues: criticalIssues.count,
            highIssues: highIssues.count,
            mediumIssues: mediumIssues.count,
            lowIssues: lowIssues.count,
            recommendations: generateRecommendations(from: allIssues),
            timestamp: Date()
        )
    }
    
    private func generateRecommendations(from issues: [ValidationIssue]) -> [String] {
        var recommendations: [String] = []
        
        let criticalIssues = issues.filter { $0.severity == .critical }
        if !criticalIssues.isEmpty {
            recommendations.append("发现关键数据问题，建议检查设备连接和权限设置")
        }
        
        let outlierIssues = issues.filter { $0.type == .outlier }
        if outlierIssues.count > 2 {
            recommendations.append("数据异常值较多，建议检查佩戴方式和设备校准")
        }
        
        let inconsistentIssues = issues.filter { $0.type == .inconsistent }
        if inconsistentIssues.count > 1 {
            recommendations.append("数据一致性问题，建议保持规律的监测习惯")
        }
        
        let missingIssues = issues.filter { $0.type == .missing }
        if !missingIssues.isEmpty {
            recommendations.append("部分数据缺失，建议确保设备正常工作并授权数据访问")
        }
        
        return recommendations
    }
}

struct ValidationReport {
    let overallConfidence: Double
    let totalIssues: Int
    let criticalIssues: Int
    let highIssues: Int
    let mediumIssues: Int
    let lowIssues: Int
    let recommendations: [String]
    let timestamp: Date
    
    var qualityLevel: DataValidationService.DataQuality {
        switch overallConfidence {
        case 0.9...1.0:
            return .excellent
        case 0.7..<0.9:
            return .good
        case 0.5..<0.7:
            return .fair
        case 0.3..<0.5:
            return .poor
        default:
            return .invalid
        }
    }
    
    var hasSignificantIssues: Bool {
        return criticalIssues > 0 || highIssues > 2
    }
}