import Foundation

/**
 MultiMetricFusionEngine Extensions
 
 多指标融合算法的辅助方法和高级功能
 */
extension MultiMetricFusionEngine {
    
    // MARK: - Advanced Fusion Methods
    
    func calculateIndividualContributions(
        _ data: ProcessedMetricData,
        weights: MetricWeights
    ) -> [String: Double] {
        var contributions: [String: Double] = [:]
        
        if data.hasHRV {
            contributions["HRV"] = data.hrvStress * weights.hrv
        }
        
        if data.hasHeartRate {
            contributions["HeartRate"] = data.heartRateStress * weights.heartRate
        }
        
        if data.hasSleep {
            contributions["Sleep"] = data.sleepStress * weights.sleep
        }
        
        if data.hasContext {
            contributions["Context"] = data.contextStress * weights.context
        }
        
        return contributions
    }
    
    func applyTemporalAdjustment(
        stressLevel: Double,
        historical: [StressAnalysisEngine.StressAnalysisResult]
    ) -> Double {
        guard !historical.isEmpty else { return stressLevel }
        
        // 计算短期趋势（最近3个数据点）
        let recentResults = historical.prefix(3).map { $0.stressLevel }
        let trend = calculateTrend(recentResults)
        
        // 计算长期基线（最近10个数据点）
        let longerResults = historical.prefix(10).map { $0.stressLevel }
        let baseline = longerResults.reduce(0, +) / Double(longerResults.count)
        
        // 时间调整因子
        let trendAdjustment = trend * 0.1 // 10%的趋势影响
        let baselineAdjustment = (stressLevel - baseline) * 0.05 // 5%的基线偏差影响
        
        let adjustedStress = stressLevel + trendAdjustment + baselineAdjustment
        return max(0.0, min(1.0, adjustedStress))
    }
    
    func updateHistoricalData(_ result: FusionResult) {
        historicalResults.append(result)
        
        // 保持历史记录在限制范围内
        if historicalResults.count > maxHistorySize {
            historicalResults.removeFirst(historicalResults.count - maxHistorySize)
        }
    }
    
    func updateAdaptiveWeights(_ result: FusionResult) {
        // 基于结果质量调整自适应权重
        let learningRate = FusionConfig.adaptiveLearningRate
        
        // 如果结果置信度高，向该权重组合调整
        if result.confidence > FusionConfig.confidenceThreshold {
            adaptiveWeights = MetricWeights(
                hrv: adaptiveWeights.hrv + (result.weights.hrv - adaptiveWeights.hrv) * learningRate,
                heartRate: adaptiveWeights.heartRate + (result.weights.heartRate - adaptiveWeights.heartRate) * learningRate,
                sleep: adaptiveWeights.sleep + (result.weights.sleep - adaptiveWeights.sleep) * learningRate,
                context: adaptiveWeights.context + (result.weights.context - adaptiveWeights.context) * learningRate,
                temporal: adaptiveWeights.temporal + (result.weights.temporal - adaptiveWeights.temporal) * learningRate
            ).normalized()
        }
    }
    
    func adaptWeightsBasedOnHistory(
        _ data: ProcessedMetricData,
        historical: [StressAnalysisEngine.StressAnalysisResult]
    ) -> MetricWeights {
        guard !historical.isEmpty else { return adaptiveWeights }
        
        // 分析历史数据中各指标的表现
        var hrvAccuracy: Double = 0.0
        var heartRateAccuracy: Double = 0.0
        var sleepAccuracy: Double = 0.0
        var contextAccuracy: Double = 0.0
        
        var validComparisons = 0
        
        // 计算各指标的历史准确性
        for i in 1..<min(historical.count, 10) {
            let current = historical[i-1]
            let previous = historical[i]
            
            // 简化的准确性计算：预测下一个值的能力
            let actualChange = current.stressLevel - previous.stressLevel
            
            if let currentHRV = current.contributingFactors.first(where: { $0.type == .hrv })?.impact,
               let previousHRV = previous.contributingFactors.first(where: { $0.type == .hrv })?.impact {
                let predictedChange = (currentHRV - previousHRV) * 0.1
                hrvAccuracy += 1.0 - abs(actualChange - predictedChange)
                validComparisons += 1
            }
            
            // 类似地计算其他指标...
        }
        
        if validComparisons > 0 {
            hrvAccuracy /= Double(validComparisons)
            // 其他指标的准确性计算...
        }
        
        // 基于准确性调整权重
        var adjustedWeights = adaptiveWeights
        
        if hrvAccuracy > 0.7 {
            adjustedWeights = MetricWeights(
                hrv: min(FusionConfig.hrvWeightRange.upperBound, adjustedWeights.hrv * 1.1),
                heartRate: adjustedWeights.heartRate,
                sleep: adjustedWeights.sleep,
                context: adjustedWeights.context,
                temporal: adjustedWeights.temporal
            )
        } else if hrvAccuracy < 0.3 {
            adjustedWeights = MetricWeights(
                hrv: max(FusionConfig.hrvWeightRange.lowerBound, adjustedWeights.hrv * 0.9),
                heartRate: adjustedWeights.heartRate,
                sleep: adjustedWeights.sleep,
                context: adjustedWeights.context,
                temporal: adjustedWeights.temporal
            )
        }
        
        return adjustedWeights.normalized()
    }
    
    // MARK: - Bayesian Methods
    
    func updateBayesianPosterior(
        _ prior: Double,
        likelihood: Double,
        evidence: Double
    ) -> Double {
        // 简化贝叶斯更新
        let numerator = likelihood * prior * evidence
        let denominator = numerator + (1.0 - prior) * (1.0 - likelihood) * evidence
        
        return denominator > 0 ? numerator / denominator : prior
    }
    
    // MARK: - Fuzzy Logic Methods
    
    func createFuzzySet(center: Double, width: Double) -> (Double) -> Double {
        return { value in
            let distance = abs(value - center)
            return max(0.0, 1.0 - distance / width)
        }
    }
    
    func evaluateFuzzySet(_ fuzzySet: (Double) -> Double, value: Double) -> Double {
        return fuzzySet(value)
    }
    
    // MARK: - Statistical Methods
    
    func calculateTrend(_ values: [Double]) -> Double {
        guard values.count >= 2 else { return 0.0 }
        
        let n = Double(values.count)
        let indices = Array(0..<values.count).map(Double.init)
        
        let sumX = indices.reduce(0, +)
        let sumY = values.reduce(0, +)
        let sumXY = zip(indices, values).map(*).reduce(0, +)
        let sumX2 = indices.map { $0 * $0 }.reduce(0, +)
        
        let denominator = n * sumX2 - sumX * sumX
        guard denominator != 0 else { return 0.0 }
        
        return (n * sumXY - sumX * sumY) / denominator
    }
    
    func detectOutliers(_ values: [Double]) -> [Int] {
        guard values.count >= 3 else { return [] }
        
        let mean = values.reduce(0, +) / Double(values.count)
        let stdDev = sqrt(values.map { pow($0 - mean, 2) }.reduce(0, +) / Double(values.count - 1))
        
        var outlierIndices: [Int] = []
        
        for (index, value) in values.enumerated() {
            let zScore = abs(value - mean) / stdDev
            if zScore > FusionConfig.outlierThreshold {
                outlierIndices.append(index)
            }
        }
        
        return outlierIndices
    }
    
    // MARK: - Confidence Calculations
    
    func calculateCombinedConfidence(
        _ confidences: [Double],
        weights: [Double]
    ) -> Double {
        guard confidences.count == weights.count, !confidences.isEmpty else { return 0.0 }
        
        // 加权置信度计算
        let weightedSum = zip(confidences, weights).map(*).reduce(0, +)
        let totalWeight = weights.reduce(0, +)
        
        guard totalWeight > 0 else { return 0.0 }
        
        return weightedSum / totalWeight
    }
    
    func calculateUncertaintyPropagation(
        _ uncertainties: [Double],
        correlations: [[Double]]? = nil
    ) -> Double {
        guard !uncertainties.isEmpty else { return 1.0 }
        
        if let correlations = correlations, correlations.count == uncertainties.count {
            // 考虑相关性的不确定性传播
            var totalVariance: Double = 0.0
            
            for i in 0..<uncertainties.count {
                for j in 0..<uncertainties.count {
                    let correlation = i == j ? 1.0 : correlations[i][j]
                    totalVariance += uncertainties[i] * uncertainties[j] * correlation
                }
            }
            
            return sqrt(max(0.0, totalVariance))
        } else {
            // 简化的不确定性传播（假设独立）
            let combinedVariance = uncertainties.map { $0 * $0 }.reduce(0, +)
            return sqrt(combinedVariance)
        }
    }
    
    // MARK: - Quality Assurance
    
    func validateFusionResult(_ result: FusionResult) -> (isValid: Bool, issues: [String]) {
        var issues: [String] = []
        
        // 检查压力水平范围
        if result.fusedStressLevel < 0.0 || result.fusedStressLevel > 1.0 {
            issues.append("压力水平超出有效范围 [0,1]: \(result.fusedStressLevel)")
        }
        
        // 检查置信度范围
        if result.confidence < 0.0 || result.confidence > 1.0 {
            issues.append("置信度超出有效范围 [0,1]: \(result.confidence)")
        }
        
        // 检查权重归一化
        if !result.weights.isNormalized {
            issues.append("权重未正确归一化")
        }
        
        // 检查数据完整性
        if result.qualityMetrics.dataCompleteness < 0.3 {
            issues.append("数据完整性过低: \(result.qualityMetrics.dataCompleteness)")
        }
        
        // 检查置信度阈值
        if result.confidence < FusionConfig.confidenceThreshold {
            issues.append("置信度低于阈值: \(result.confidence) < \(FusionConfig.confidenceThreshold)")
        }
        
        return (issues.isEmpty, issues)
    }
    
    func optimizeFusionParameters() {
        // 基于历史性能优化融合参数
        guard historicalResults.count >= 10 else { return }
        
        let recentResults = historicalResults.suffix(10)
        
        // 计算平均置信度
        let avgConfidence = recentResults.map { $0.confidence }.reduce(0, +) / Double(recentResults.count)
        
        // 如果平均置信度较低，调整参数
        if avgConfidence < FusionConfig.confidenceThreshold {
            // 可以在这里实现参数优化逻辑
            print("优化融合参数：当前平均置信度 \(avgConfidence)")
        }
    }
    
    // MARK: - Reporting and Analysis
    
    func generateFusionReport() -> String {
        var report = "=== 多指标融合分析报告 ===\n\n"
        
        report += "当前自适应权重:\n"
        report += "- HRV: \(String(format: "%.3f", adaptiveWeights.hrv))\n"
        report += "- 心率: \(String(format: "%.3f", adaptiveWeights.heartRate))\n"
        report += "- 睡眠: \(String(format: "%.3f", adaptiveWeights.sleep))\n"
        report += "- 上下文: \(String(format: "%.3f", adaptiveWeights.context))\n\n"
        
        if !historicalResults.isEmpty {
            let recentResults = historicalResults.suffix(5)
            let avgStress = recentResults.map { $0.fusedStressLevel }.reduce(0, +) / Double(recentResults.count)
            let avgConfidence = recentResults.map { $0.confidence }.reduce(0, +) / Double(recentResults.count)
            
            report += "最近5次分析:\n"
            report += "- 平均压力水平: \(String(format: "%.3f", avgStress))\n"
            report += "- 平均置信度: \(String(format: "%.3f", avgConfidence))\n"
        }
        
        return report
    }
}