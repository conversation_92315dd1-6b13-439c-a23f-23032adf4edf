import Foundation
import CoreData
import HealthKit

class HealthKitDataCache {
    
    // MARK: - Core Data Context
    
    private let context: NSManagedObjectContext
    private let backgroundContext: NSManagedObjectContext
    
    // MARK: - Cache Configuration
    
    private struct CacheConfig {
        static let maxCacheAge: TimeInterval = 24 * 60 * 60 // 24小时
        static let batchSize: Int = 100
        static let maxCacheSize: Int = 10000 // 最大缓存条目数
    }
    
    // MARK: - Cache Statistics
    
    struct CacheStatistics {
        let totalCachedItems: Int
        let hrvDataCount: Int
        let heartRateDataCount: Int
        let sleepDataCount: Int
        let oldestCacheDate: Date?
        let newestCacheDate: Date?
        let cacheSize: Int // in bytes
    }
    
    init(context: NSManagedObjectContext) {
        self.context = context
        self.backgroundContext = NSPersistentContainer(name: "StressBalanz").newBackgroundContext()
        
        // 配置后台上下文
        backgroundContext.automaticallyMergesChangesFromParent = true
        backgroundContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        // 启动时清理过期缓存
        Task {
            await cleanupExpiredCache()
        }
    }
    
    // MARK: - HRV Data Caching
    
    func cacheHRVData(_ samples: [HKQuantitySample]) async {
        await performBackgroundTask { context in
            for sample in samples {
                // 检查是否已缓存
                if !self.isDataCached(sample: sample, dataType: "HRV", in: context) {
                    let healthKitData = HealthKitData(context: context)
                    healthKitData.id = UUID()
                    healthKitData.timestamp = sample.endDate
                    healthKitData.dataType = "HRV"
                    healthKitData.value = sample.quantity.doubleValue(for: HKUnit.secondUnit(with: .milli))
                    healthKitData.unit = "ms"
                    healthKitData.sourceBundle = sample.sourceRevision.source.bundleIdentifier
                    healthKitData.isProcessed = false
                    
                    // 缓存元数据
                    if let metadata = sample.metadata {
                        healthKitData.metadata = metadata as NSObject
                    }
                }
            }
            
            try? context.save()
        }
    }
    
    func getCachedHRVData(from startDate: Date, to endDate: Date) async -> [HealthKitData] {
        return await performBackgroundFetch { context in
            let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            request.predicate = NSPredicate(
                format: "dataType == %@ AND timestamp >= %@ AND timestamp <= %@",
                "HRV", startDate as NSDate, endDate as NSDate
            )
            request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: false)]
            
            return (try? context.fetch(request)) ?? []
        }
    }
    
    // MARK: - Heart Rate Data Caching
    
    func cacheHeartRateData(_ samples: [HKQuantitySample]) async {
        await performBackgroundTask { context in
            for sample in samples {
                if !self.isDataCached(sample: sample, dataType: "HeartRate", in: context) {
                    let healthKitData = HealthKitData(context: context)
                    healthKitData.id = UUID()
                    healthKitData.timestamp = sample.endDate
                    healthKitData.dataType = "HeartRate"
                    healthKitData.value = sample.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
                    healthKitData.unit = "bpm"
                    healthKitData.sourceBundle = sample.sourceRevision.source.bundleIdentifier
                    healthKitData.isProcessed = false
                    
                    if let metadata = sample.metadata {
                        healthKitData.metadata = metadata as NSObject
                    }
                }
            }
            
            try? context.save()
        }
    }
    
    func getCachedHeartRateData(from startDate: Date, to endDate: Date) async -> [HealthKitData] {
        return await performBackgroundFetch { context in
            let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            request.predicate = NSPredicate(
                format: "dataType == %@ AND timestamp >= %@ AND timestamp <= %@",
                "HeartRate", startDate as NSDate, endDate as NSDate
            )
            request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: false)]
            
            return (try? context.fetch(request)) ?? []
        }
    }
    
    // MARK: - Sleep Data Caching
    
    func cacheSleepData(_ samples: [HKCategorySample]) async {
        await performBackgroundTask { context in
            for sample in samples {
                if !self.isDataCached(sample: sample, dataType: "Sleep", in: context) {
                    let healthKitData = HealthKitData(context: context)
                    healthKitData.id = UUID()
                    healthKitData.timestamp = sample.endDate
                    healthKitData.dataType = "Sleep"
                    healthKitData.value = Double(sample.value)
                    healthKitData.unit = "category"
                    healthKitData.sourceBundle = sample.sourceRevision.source.bundleIdentifier
                    healthKitData.isProcessed = false
                    
                    // 存储睡眠阶段信息
                    var sleepMetadata = sample.metadata ?? [:]
                    sleepMetadata["startDate"] = sample.startDate
                    sleepMetadata["endDate"] = sample.endDate
                    sleepMetadata["duration"] = sample.endDate.timeIntervalSince(sample.startDate)
                    
                    healthKitData.metadata = sleepMetadata as NSObject
                }
            }
            
            try? context.save()
        }
    }
    
    func getCachedSleepData(from startDate: Date, to endDate: Date) async -> [HealthKitData] {
        return await performBackgroundFetch { context in
            let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            request.predicate = NSPredicate(
                format: "dataType == %@ AND timestamp >= %@ AND timestamp <= %@",
                "Sleep", startDate as NSDate, endDate as NSDate
            )
            request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: false)]
            
            return (try? context.fetch(request)) ?? []
        }
    }
    
    // MARK: - Cache Management
    
    func cleanupExpiredCache() async {
        let expirationDate = Date().addingTimeInterval(-CacheConfig.maxCacheAge)
        
        await performBackgroundTask { context in
            let request: NSFetchRequest<NSFetchRequestResult> = HealthKitData.fetchRequest()
            request.predicate = NSPredicate(format: "timestamp < %@", expirationDate as NSDate)
            
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: request)
            deleteRequest.resultType = .resultTypeObjectIDs
            
            do {
                let result = try context.execute(deleteRequest) as? NSBatchDeleteResult
                let objectIDArray = result?.result as? [NSManagedObjectID]
                let changes = [NSDeletedObjectsKey: objectIDArray ?? []]
                NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [context])
                
                print("清理了过期的缓存数据")
            } catch {
                print("清理缓存失败: \(error.localizedDescription)")
            }
        }
    }
    
    func cleanupOldestCache(keepCount: Int = CacheConfig.maxCacheSize) async {
        await performBackgroundTask { context in
            // 获取所有缓存数据的数量
            let countRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            let totalCount = (try? context.count(for: countRequest)) ?? 0
            
            if totalCount > keepCount {
                let deleteCount = totalCount - keepCount
                
                // 获取最旧的数据
                let request: NSFetchRequest<NSFetchRequestResult> = HealthKitData.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
                request.fetchLimit = deleteCount
                
                let deleteRequest = NSBatchDeleteRequest(fetchRequest: request)
                deleteRequest.resultType = .resultTypeObjectIDs
                
                do {
                    let result = try context.execute(deleteRequest) as? NSBatchDeleteResult
                    let objectIDArray = result?.result as? [NSManagedObjectID]
                    let changes = [NSDeletedObjectsKey: objectIDArray ?? []]
                    NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [context])
                    
                    print("清理了\(deleteCount)条最旧的缓存数据")
                } catch {
                    print("清理旧缓存失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    func clearAllCache() async {
        await performBackgroundTask { context in
            let request: NSFetchRequest<NSFetchRequestResult> = HealthKitData.fetchRequest()
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: request)
            deleteRequest.resultType = .resultTypeObjectIDs
            
            do {
                let result = try context.execute(deleteRequest) as? NSBatchDeleteResult
                let objectIDArray = result?.result as? [NSManagedObjectID]
                let changes = [NSDeletedObjectsKey: objectIDArray ?? []]
                NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [context])
                
                print("已清空所有缓存数据")
            } catch {
                print("清空缓存失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Cache Statistics
    
    func getCacheStatistics() async -> CacheStatistics {
        return await performBackgroundFetch { context in
            let totalRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            let totalCount = (try? context.count(for: totalRequest)) ?? 0
            
            let hrvRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            hrvRequest.predicate = NSPredicate(format: "dataType == %@", "HRV")
            let hrvCount = (try? context.count(for: hrvRequest)) ?? 0
            
            let heartRateRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            heartRateRequest.predicate = NSPredicate(format: "dataType == %@", "HeartRate")
            let heartRateCount = (try? context.count(for: heartRateRequest)) ?? 0
            
            let sleepRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            sleepRequest.predicate = NSPredicate(format: "dataType == %@", "Sleep")
            let sleepCount = (try? context.count(for: sleepRequest)) ?? 0
            
            // 获取时间范围
            let oldestRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            oldestRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
            oldestRequest.fetchLimit = 1
            let oldestData = try? context.fetch(oldestRequest).first
            
            let newestRequest: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            newestRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: false)]
            newestRequest.fetchLimit = 1
            let newestData = try? context.fetch(newestRequest).first
            
            return CacheStatistics(
                totalCachedItems: totalCount,
                hrvDataCount: hrvCount,
                heartRateDataCount: heartRateCount,
                sleepDataCount: sleepCount,
                oldestCacheDate: oldestData?.timestamp,
                newestCacheDate: newestData?.timestamp,
                cacheSize: totalCount * 200 // 大约估算每条记录200字节
            )
        }
    }
    
    // MARK: - Cache Optimization
    
    func optimizeCache() async {
        // 1. 清理过期数据
        await cleanupExpiredCache()
        
        // 2. 清理过多的数据
        await cleanupOldestCache()
        
        // 3. 标记已处理的数据
        await markProcessedData()
        
        print("缓存优化完成")
    }
    
    private func markProcessedData() async {
        await performBackgroundTask { context in
            let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
            request.predicate = NSPredicate(format: "isProcessed == NO")
            request.fetchLimit = CacheConfig.batchSize
            
            if let unprocessedData = try? context.fetch(request) {
                for data in unprocessedData {
                    // 这里可以添加数据处理逻辑
                    // 例如：计算压力水平、数据分析等
                    data.isProcessed = true
                }
                
                try? context.save()
                print("标记了\(unprocessedData.count)条数据为已处理")
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func isDataCached(sample: HKSample, dataType: String, in context: NSManagedObjectContext) -> Bool {
        let request: NSFetchRequest<HealthKitData> = HealthKitData.fetchRequest()
        request.predicate = NSPredicate(
            format: "dataType == %@ AND timestamp == %@ AND sourceBundle == %@",
            dataType,
            sample.endDate as NSDate,
            sample.sourceRevision.source.bundleIdentifier
        )
        request.fetchLimit = 1
        
        return ((try? context.count(for: request)) ?? 0) > 0
    }
    
    private func performBackgroundTask<T>(_ task: @escaping (NSManagedObjectContext) throws -> T) async -> T? {
        return await withCheckedContinuation { continuation in
            backgroundContext.perform {
                do {
                    let result = try task(self.backgroundContext)
                    continuation.resume(returning: result)
                } catch {
                    print("后台任务执行失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    private func performBackgroundFetch<T>(_ fetch: @escaping (NSManagedObjectContext) throws -> T) async -> T {
        return await withCheckedContinuation { continuation in
            backgroundContext.perform {
                do {
                    let result = try fetch(self.backgroundContext)
                    continuation.resume(returning: result)
                } catch {
                    print("后台获取失败: \(error.localizedDescription)")
                    // 返回默认值，具体类型需要根据泛型T确定
                    if T.self == [HealthKitData].self {
                        continuation.resume(returning: [] as! T)
                    } else if T.self == CacheStatistics.self {
                        let emptyStats = CacheStatistics(
                            totalCachedItems: 0,
                            hrvDataCount: 0,
                            heartRateDataCount: 0,
                            sleepDataCount: 0,
                            oldestCacheDate: nil,
                            newestCacheDate: nil,
                            cacheSize: 0
                        )
                        continuation.resume(returning: emptyStats as! T)
                    } else {
                        // 对于其他类型，这里需要根据具体情况处理
                        fatalError("Unsupported return type in performBackgroundFetch")
                    }
                }
            }
        }
    }
}

// MARK: - Cache Management Extensions

extension HealthKitDataCache {
    
    func preloadRecentData() async {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -7, to: endDate) ?? endDate
        
        // 检查是否需要预加载数据
        let cachedData = await getCachedHRVData(from: startDate, to: endDate)
        
        if cachedData.isEmpty {
            print("开始预加载最近7天的数据")
            // 这里应该触发从HealthKit获取数据的流程
            NotificationCenter.default.post(name: .cachePreloadRequested, object: nil)
        }
    }
    
    func syncWithHealthKit() async {
        // 获取最新缓存数据的时间戳
        let statistics = await getCacheStatistics()
        let lastCacheDate = statistics.newestCacheDate ?? Date().addingTimeInterval(-24 * 60 * 60)
        
        // 请求同步从最后缓存时间到现在的数据
        let syncInfo = [
            "startDate": lastCacheDate,
            "endDate": Date()
        ]
        
        NotificationCenter.default.post(
            name: .cacheSyncRequested,
            object: syncInfo
        )
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let cachePreloadRequested = Notification.Name("cachePreloadRequested")
    static let cacheSyncRequested = Notification.Name("cacheSyncRequested")
    static let cacheCleanupCompleted = Notification.Name("cacheCleanupCompleted")
}