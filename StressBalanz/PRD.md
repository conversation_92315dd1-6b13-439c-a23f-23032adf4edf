# StressBalanz App 产品需求文档 (PRD)

版本：V1.0  
日期：2025年7月12日

---

## 1. 产品概述

### 1.1 产品愿景

“StressBalanz”是一款基于科学的个人压力管理与心理健康伴侣。我们致力于通过科技的力量，帮助用户清晰地认知自己的压力状态，学习并实践有效的缓解方法，最终实现更健康、更平和的心理状态。

### 1.2 问题陈述

现代生活节奏快，无论是职场人士、学生还是其他社会角色，都面临着不同程度的压力。然而，压力是一种无形的主观感受，用户往往难以量化和察觉其具体程度和来源，也缺乏简单、有效、科学的工具来主动管理和缓解压力。许多人直到出现明显的生理或心理症状时，才意识到问题的严重性。

### 1.3 目标用户

- **高压职场人士 (25-45岁)**：面临工作KPI、人际关系、家庭生活等多重压力，希望提高情绪调节能力和工作效率。
- **备考学生 (16-25岁)**：面临学业、升学、社交等多重压力，需要快速有效的方法来缓解考前焦虑和日常紧张情绪。
- **关注身心健康的普通用户 (所有年龄段)**：对个人健康数据敏感，希望通过科技手段更好地了解和改善自己的身心状态。

### 1.4 核心目标

- **测量与认知**：利用 Apple Watch 的健康数据（如心率变异性HRV、静息心率等），建立科学的压力评估模型，让用户直观地“看见”自己的压力水平。
- **干预与缓解**：提供一系列经科学验证的压力缓解练习（如呼吸训练、正念冥想），并提供实时生物反馈，引导用户进行有效干预。
- **追踪与洞察**：长期记录用户的压力数据和干预效果，通过数据统计和图表分析，帮助用户发现压力模式，并持续改善。

---

## 2. 功能详述 (Features)

### 2.1 核心功能一：压力测量与可视化

**用户故事**：作为用户，我希望能随时了解自己当前的压力水平，并且这种展示不是一个冰冷的数字，而是能让我直观感受到的、生动的画面。

**功能描述**：
应用首页的核心模块，通过算法解读 HealthKit 中的数据，生成一个实时的“压力指数”，并以动画形式呈现。

#### 具体需求：

##### 数据接入

- 首次启动时，引导用户授权访问 Apple Health (HealthKit) 数据。
- 必须获取的数据权限：心率变异性 (HRV)、静息心率、睡眠分析、站立心率、体能训练。
- 向用户清晰说明为何需要这些数据，并强调所有数据仅在本地处理，确保隐私安全。

##### 压力指数算法

- **核心算法 V1.0**：以 HRV 为最主要指标，HRV 越低，代表压力水平可能越高。
- **辅助算法**：结合静息心率（长期升高可能与慢性压力有关）和近期的睡眠质量，对压力指数进行加权调整。
- 算法需持续迭代优化，未来可引入更多维度（如用户主观反馈、活动量等）。

##### 压力可视化

- 设计动态、抽象的视觉元素来代表压力，例如：
  - **能量球**：压力低为蓝绿，流动平缓稳定；压力高为红紫，流动加速并出现“毛刺”。
  - **平静湖面**：压力低时湖面平静，压力高时波涛汹涌，天气阴沉。
- **交互**：用户可触摸与动画互动，获得沉浸体验。

##### 实时与历史视图

- 首页显示“当前”压力状态。
- 提供时间轴切换（日/周/月），可回顾过去任意时间点的压力可视化与指数。

---

### 2.2 核心功能二：科学缓解训练

**用户故事**：当我感到压力大时，我希望App能立刻给我一个简单有效的放松方法，并引导我完成它。

**功能描述**：
提供包含多种减压练习的工具库，所有练习配有详细指导和实时生物反馈。

#### 具体需求：

##### 呼吸训练

- 提供多种呼吸模式（如 4-7-8 呼吸法、箱式呼吸法）。
- 通过视觉动画（放大缩小的圆圈）和 Apple Watch 的 Taptic Engine 引导吸气、屏住、呼气。
- 实时监测心率和 HRV 变化，训练结束后生成报告，显示前后对比，量化效果。

##### 正念冥想

- 提供引导式冥想音频库，按场景（入睡前、工作休息、考前焦虑）和时长（3/5/10 分钟）分类。
- 完成冥想后记录“正念分钟数”到 Apple Health。

##### 身体扫描与放松

- 通过音频引导，将注意力集中在身体各部位，感受并释放紧张感。

##### 智能推荐

- 当压力指数持续偏高时，主动发送低打扰通知，推荐短时放松练习。

---

### 2.3 核心功能三：数据统计与洞察

**用户故事**：我希望能回顾一周或一个月的压力变化，了解哪些时候压力最大，以及放松练习是否有效。

**功能描述**：
整合用户压力数据、干预行为和效果，以图表与报告呈现，建立“行为-压力-结果”认知闭环。

#### 具体需求：

##### 数据图表

- **压力趋势图**：日/周/月维度压力指数曲线。
- **HRV 与静息心率图**：核心生理指标长期趋势。
- **训练日历**：每日完成的缓解训练，标记类型与时长。

##### 关联分析 (V1.1 功能)

- 用户可手动标记高压力事件（如开会、考试）。
- 未来与日历 App 打通，自动关联压力数据与日程事件，识别压力源。

##### 周报/月报

- 自动生成报告，包括：
  - 平均压力指数及环比变化。
  - 压力最高/最低的一天。
  - 总训练时长、最常用方法。
  - 个性化建议（如周一压力高，建议上午正念练习）。

---

### 2.4 核心功能四：Apple Watch 端应用

**用户故事**：我希望不掏出手机，只用手表就能快速查看压力或开始呼吸练习。

**功能描述**：
手机 App 的延伸，提供核心功能快速访问。

#### 具体需求：

##### 表盘复杂功能 (Complication)

- 提供多样表盘组件，显示实时压力指数（用颜色或图标表示）。
- 点击直接启动 watchOS App。

##### 独立 App

- 显示同步的压力可视化动画和指数。
- 一键启动预设呼吸训练。
- 快速记录当前主观感受（如开心、平静、紧张、疲惫）。

---

## 3. 非功能性需求

### 3.1 设计与用户体验 (UI/UX)

- 设计风格：极简、干净、治愈；主色调建议柔和蓝/绿/渐变色。
- 动效：流畅自然，符合物理直觉，创造沉浸平静体验。
- 字体：无衬线字体，支持动态字体大小。
- 无障碍：支持 VoiceOver 等辅助功能。

### 3.2 技术要求

- 平台：iOS 17+，watchOS 10+
- 开发语言：Swift
- UI 框架：SwiftUI
- 数据存储：Core Data / SwiftData 本地持久化，iCloud 同步
- 隐私与安全：所有 HealthKit 数据仅本地处理，不上传服务器；隐私政策需明确数据使用方式。

---

## 4. 未来路线图 (Roadmap)

### V1.1

- 智能日志：记录影响压力的日常活动，并与压力数据关联分析。
- CBT 微练习：引入认知行为疗法元素，如“思维日记”、“感恩练习”。
- 更多训练内容：与心理学专家合作，新增冥想和放松音频。

### V2.0

- 个性化方案：基于长期数据与偏好，机器学习生成每周减压计划。
- 环境音/白噪音：集成下雨、森林、海浪等声音，帮助专注或放松。
- 社交连接（可选）：允许用户分享压力报告给咨询师或家人。

---

## 5. 成功指标 (KPIs)

- **用户活跃度**：DAU / MAU
- **功能使用率**：缓解训练完成率、每日查看压力指数用户比例
- **用户留存**：次日、7日、30日留存率
- **健康数据改善**：长期用户平均 HRV 提升、静息心率下降
- **用户满意度**：App Store 评分与评论

---

> 本文档为 “心晴” V1.0 产品需求文档，未来将根据用户调研与数据验证持续更新。

---

**Q1.** 是否需要为此 PRD 生成详细的 **任务拆解与开发优先级排期**？  
**Q2.** 是否需要输出对应的 **设计概念稿关键词与动效探索方向**？  
**Q3.** 是否需要同时准备 **Pitch Deck 架构**，以便对外沟通和投资人展示？

xxxxxxxxx
