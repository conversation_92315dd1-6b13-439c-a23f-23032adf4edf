# StressBalanz 技术架构设计与实施策略

**版本**: V1.0  
**制定日期**: 2025年7月12日  
**目标平台**: iOS 17.0+, watchOS 10+  
**开发语言**: Swift 5.0+

---

## 📐 系统架构总览

### 架构设计原则

**🎯 单一职责原则**: 每个模块都有明确的单一职责，降低模块间耦合度  
**🔄 开放封闭原则**: 对扩展开放，对修改封闭，便于功能迭代  
**🧩 依赖注入原则**: 通过依赖注入实现模块间的松耦合  
**📊 数据驱动设计**: 以数据流为中心设计系统架构  
**🔒 隐私优先原则**: 所有敏感数据处理均在本地完成

### 总体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    StressBalanz 应用层                        │
├─────────────────────────────────────────────────────────────┤
│  iOS App (SwiftUI)           │  watchOS App (SwiftUI)       │
│  ├── 主界面 (MainView)        │  ├── 表盘复杂功能             │
│  ├── 可视化 (VisualizationView)│  ├── 快速训练                │
│  ├── 训练模块 (TrainingViews)  │  ├── 压力显示                │
│  └── 统计分析 (AnalyticsView)  │  └── 同步管理                │
├─────────────────────────────────────────────────────────────┤
│                     业务逻辑层 (Business Logic)               │
│  ├── 压力算法引擎 (StressAlgorithmEngine)                     │
│  ├── 训练管理器 (TrainingManager)                           │
│  ├── 数据分析引擎 (AnalyticsEngine)                          │
│  ├── 通知管理器 (NotificationManager)                       │
│  └── 同步协调器 (SyncCoordinator)                           │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Layer)                   │
│  ├── HealthKit适配器 (HealthKitAdapter)                      │
│  ├── Core Data管理器 (CoreDataManager)                      │
│  ├── iCloud同步器 (CloudSyncManager)                        │
│  └── 缓存管理器 (CacheManager)                              │
├─────────────────────────────────────────────────────────────┤
│                   基础设施层 (Infrastructure)                │
│  ├── 网络监控 (NetworkMonitor)                             │
│  ├── 设备信息 (DeviceInfoProvider)                         │
│  ├── 安全管理器 (SecurityManager)                          │
│  └── 日志系统 (LoggingSystem)                              │
└─────────────────────────────────────────────────────────────┘
```

---

## 🏗️ 详细模块设计

### 1. 数据层架构 (Data Layer)

#### 1.1 Core Data 模型设计

```swift
// MARK: - Core Data Stack
class CoreDataStack {
    static let shared = CoreDataStack()
    
    lazy var persistentContainer: NSPersistentCloudKitContainer = {
        let container = NSPersistentCloudKitContainer(name: "StressBalanz")
        
        // iCloud同步配置
        container.persistentStoreDescriptions.first?.setOption(true as NSNumber, 
                                                               forKey: NSPersistentHistoryTrackingKey)
        container.persistentStoreDescriptions.first?.setOption(true as NSNumber, 
                                                               forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Core Data加载失败: \(error)")
            }
        }
        
        container.viewContext.automaticallyMergesChangesFromParent = true
        return container
    }()
    
    var context: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    func save() {
        guard context.hasChanges else { return }
        
        do {
            try context.save()
        } catch {
            print("Core Data保存失败: \(error)")
        }
    }
}

// MARK: - 数据实体定义
@objc(StressSession)
public class StressSession: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var timestamp: Date
    @NSManaged public var stressLevel: Double
    @NSManaged public var hrvScore: Double
    @NSManaged public var heartRate: Double
    @NSManaged public var associatedEvents: String?
    @NSManaged public var trainingSession: TrainingSession?
}

@objc(TrainingSession)
public class TrainingSession: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var type: String
    @NSManaged public var duration: TimeInterval
    @NSManaged public var completionRate: Double
    @NSManaged public var preStressLevel: Double
    @NSManaged public var postStressLevel: Double
    @NSManaged public var effectiveness: Double
    @NSManaged public var stressSession: StressSession?
}
```

#### 1.2 HealthKit 数据访问层

```swift
// MARK: - HealthKit 数据管理器
class HealthKitManager: ObservableObject {
    private let healthStore = HKHealthStore()
    private let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN)!
    private let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
    private let sleepType = HKCategoryType.categoryType(forIdentifier: .sleepAnalysis)!
    
    // 数据发布者
    @Published var currentHRV: Double = 0
    @Published var currentHeartRate: Double = 0
    @Published var isAuthorized: Bool = false
    
    // Combine Publishers for reactive programming
    var hrvPublisher: AnyPublisher<Double, Never> {
        $currentHRV.eraseToAnyPublisher()
    }
    
    var heartRatePublisher: AnyPublisher<Double, Never> {
        $currentHeartRate.eraseToAnyPublisher()
    }
    
    init() {
        requestAuthorization()
        setupObservers()
    }
    
    // MARK: - 权限请求
    private func requestAuthorization() {
        let readTypes: Set<HKSampleType> = [hrvType, heartRateType, sleepType]
        let writeTypes: Set<HKSampleType> = [HKQuantityType.quantityType(forIdentifier: .mindfulSession)!]
        
        healthStore.requestAuthorization(toShare: writeTypes, read: readTypes) { [weak self] success, error in
            DispatchQueue.main.async {
                self?.isAuthorized = success
                if success {
                    self?.startDataCollection()
                }
            }
        }
    }
    
    // MARK: - 实时数据监听
    private func setupObservers() {
        // HRV数据观察者
        let hrvQuery = HKObserverQuery(sampleType: hrvType, predicate: nil) { [weak self] _, _, error in
            guard error == nil else { return }
            self?.fetchLatestHRV()
        }
        
        // 心率数据观察者  
        let heartRateQuery = HKObserverQuery(sampleType: heartRateType, predicate: nil) { [weak self] _, _, error in
            guard error == nil else { return }
            self?.fetchLatestHeartRate()
        }
        
        healthStore.execute(hrvQuery)
        healthStore.execute(heartRateQuery)
    }
    
    // MARK: - 数据获取方法
    private func fetchLatestHRV() {
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        let query = HKSampleQuery(sampleType: hrvType, predicate: nil, limit: 1, sortDescriptors: [sortDescriptor]) { [weak self] _, samples, error in
            
            guard let sample = samples?.first as? HKQuantitySample else { return }
            let hrvValue = sample.quantity.doubleValue(for: HKUnit.secondUnit(with: .milli))
            
            DispatchQueue.main.async {
                self?.currentHRV = hrvValue
            }
        }
        healthStore.execute(query)
    }
    
    private func fetchLatestHeartRate() {
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        let query = HKSampleQuery(sampleType: heartRateType, predicate: nil, limit: 1, sortDescriptors: [sortDescriptor]) { [weak self] _, samples, error in
            
            guard let sample = samples?.first as? HKQuantitySample else { return }
            let heartRateValue = sample.quantity.doubleValue(for: HKUnit(from: "count/min"))
            
            DispatchQueue.main.async {
                self?.currentHeartRate = heartRateValue
            }
        }
        healthStore.execute(query)
    }
}
```

### 2. 业务逻辑层 (Business Logic)

#### 2.1 压力算法引擎

```swift
// MARK: - 压力评估算法引擎
class StressAlgorithmEngine: ObservableObject {
    @Published var currentStressLevel: Double = 0
    @Published var stressCategory: StressCategory = .low
    @Published var trend: StressTrend = .stable
    
    // 算法参数配置
    private struct AlgorithmConfig {
        static let hrvWeight: Double = 0.7
        static let heartRateWeight: Double = 0.2
        static let sleepWeight: Double = 0.1
        static let baselineWindow: TimeInterval = 7 * 24 * 3600 // 7天基线
    }
    
    enum StressCategory: String, CaseIterable {
        case low = "低"
        case moderate = "中等"
        case high = "高"
        
        var color: Color {
            switch self {
            case .low: return .green
            case .moderate: return .yellow
            case .high: return .red
            }
        }
    }
    
    enum StressTrend: String {
        case rising = "上升"
        case stable = "稳定"
        case declining = "下降"
    }
    
    // MARK: - 主要算法方法
    func calculateStressLevel(hrv: Double, heartRate: Double, sleepQuality: Double = 0.8) -> Double {
        // 获取个人基线数据
        let baseline = getUserBaseline()
        
        // HRV标准化 (值越低压力越高)
        let normalizedHRV = normalizeHRV(hrv, baseline: baseline.hrv)
        
        // 心率标准化 (值越高压力越高)
        let normalizedHeartRate = normalizeHeartRate(heartRate, baseline: baseline.heartRate)
        
        // 睡眠质量标准化 (值越低压力越高)
        let normalizedSleep = normalizeSleepQuality(sleepQuality)
        
        // 加权计算最终压力指数
        let stressScore = (normalizedHRV * AlgorithmConfig.hrvWeight +
                          normalizedHeartRate * AlgorithmConfig.heartRateWeight +
                          normalizedSleep * AlgorithmConfig.sleepWeight)
        
        // 应用平滑滤波，避免数值跳跃
        let smoothedScore = applySmoothingFilter(stressScore)
        
        // 更新趋势分析
        updateTrend(smoothedScore)
        
        DispatchQueue.main.async {
            self.currentStressLevel = smoothedScore
            self.stressCategory = self.categorizeStress(smoothedScore)
        }
        
        return smoothedScore
    }
    
    // MARK: - 辅助计算方法
    private func normalizeHRV(_ hrv: Double, baseline: Double) -> Double {
        // HRV算法：基于RMSSD的标准化
        let ratio = baseline > 0 ? hrv / baseline : 1.0
        
        // 逆向映射：HRV越低，压力分数越高
        if ratio >= 1.0 {
            return max(0, 50 - (ratio - 1.0) * 30) // HRV正常或高于基线
        } else {
            return min(100, 50 + (1.0 - ratio) * 50) // HRV低于基线
        }
    }
    
    private func normalizeHeartRate(_ heartRate: Double, baseline: Double) -> Double {
        // 心率算法：基于静息心率的偏差
        let deviation = baseline > 0 ? abs(heartRate - baseline) / baseline : 0
        return min(100, deviation * 100)
    }
    
    private func normalizeSleepQuality(_ sleepQuality: Double) -> Double {
        // 睡眠质量逆向映射：质量越低，压力分数越高
        return (1.0 - sleepQuality) * 100
    }
    
    private func applySmoothingFilter(_ newValue: Double) -> Double {
        // 指数移动平均滤波
        let alpha = 0.3
        return alpha * newValue + (1 - alpha) * currentStressLevel
    }
    
    private func categorizeStress(_ level: Double) -> StressCategory {
        switch level {
        case 0...30: return .low
        case 31...60: return .moderate
        default: return .high
        }
    }
    
    private func updateTrend(_ newLevel: Double) {
        let threshold = 5.0
        let difference = newLevel - currentStressLevel
        
        if difference > threshold {
            trend = .rising
        } else if difference < -threshold {
            trend = .declining
        } else {
            trend = .stable
        }
    }
    
    // 获取用户个人基线数据
    private func getUserBaseline() -> (hrv: Double, heartRate: Double) {
        // 从Core Data获取过去7天的平均值作为基线
        // 这里简化处理，实际应从数据库计算
        return (hrv: 35.0, heartRate: 65.0)
    }
}
```

#### 2.2 训练管理器

```swift
// MARK: - 训练管理器
class TrainingManager: ObservableObject {
    @Published var activeSession: TrainingSession?
    @Published var isTraining: Bool = false
    @Published var progress: Double = 0
    
    private var timer: Timer?
    private let stressAlgorithm = StressAlgorithmEngine()
    
    enum TrainingType: String, CaseIterable {
        case breathing = "呼吸训练"
        case meditation = "正念冥想"
        case bodyScanning = "身体扫描"
        
        var duration: TimeInterval {
            switch self {
            case .breathing: return 300 // 5分钟
            case .meditation: return 600 // 10分钟  
            case .bodyScanning: return 900 // 15分钟
            }
        }
        
        var instructions: [String] {
            switch self {
            case .breathing:
                return ["深呼吸4秒", "屏住呼吸7秒", "缓慢呼出8秒"]
            case .meditation:
                return ["专注于当下", "观察思绪流动", "保持内心平静"]
            case .bodyScanning:
                return ["从头部开始", "逐步扫描全身", "放松每个部位"]
            }
        }
    }
    
    // MARK: - 训练会话管理
    func startTraining(type: TrainingType) {
        guard !isTraining else { return }
        
        let session = TrainingSession(context: CoreDataStack.shared.context)
        session.id = UUID()
        session.type = type.rawValue
        session.duration = type.duration
        session.preStressLevel = stressAlgorithm.currentStressLevel
        
        activeSession = session
        isTraining = true
        progress = 0
        
        startTimer(duration: type.duration)
    }
    
    func pauseTraining() {
        timer?.invalidate()
        // 实现暂停逻辑
    }
    
    func resumeTraining() {
        guard let session = activeSession else { return }
        let remainingTime = session.duration * (1 - progress)
        startTimer(duration: remainingTime)
    }
    
    func stopTraining() {
        timer?.invalidate()
        
        guard let session = activeSession else { return }
        
        // 计算完成率和效果
        session.completionRate = progress
        session.postStressLevel = stressAlgorithm.currentStressLevel
        session.effectiveness = calculateEffectiveness(session)
        
        // 保存到Core Data
        CoreDataStack.shared.save()
        
        // 重置状态
        activeSession = nil
        isTraining = false
        progress = 0
    }
    
    // MARK: - 私有方法
    private func startTimer(duration: TimeInterval) {
        let interval = duration / 100 // 100个进度点
        
        timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.progress += 0.01
            
            if self?.progress ?? 0 >= 1.0 {
                self?.completeTraining()
            }
        }
    }
    
    private func completeTraining() {
        timer?.invalidate()
        progress = 1.0
        
        // 训练完成处理
        stopTraining()
        
        // 发送完成通知
        NotificationCenter.default.post(name: .trainingCompleted, object: activeSession)
    }
    
    private func calculateEffectiveness(_ session: TrainingSession) -> Double {
        let stressReduction = session.preStressLevel - session.postStressLevel
        let maxPossibleReduction = session.preStressLevel
        
        guard maxPossibleReduction > 0 else { return 0 }
        
        let effectiveness = stressReduction / maxPossibleReduction
        return max(0, min(1, effectiveness)) // 限制在0-1之间
    }
}

// MARK: - 通知扩展
extension Notification.Name {
    static let trainingCompleted = Notification.Name("trainingCompleted")
    static let stressLevelChanged = Notification.Name("stressLevelChanged")
}
```

### 3. 表现层架构 (Presentation Layer)

#### 3.1 SwiftUI 主要视图架构

```swift
// MARK: - 主应用入口
@main
struct StressBalanzApp: App {
    let persistenceController = CoreDataStack.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.context)
                .environmentObject(HealthKitManager())
                .environmentObject(StressAlgorithmEngine())
                .environmentObject(TrainingManager())
        }
    }
}

// MARK: - 主内容视图
struct ContentView: View {
    @EnvironmentObject var healthKit: HealthKitManager
    @EnvironmentObject var stressEngine: StressAlgorithmEngine
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 主页 - 压力可视化
            StressVisualizationView()
                .tabItem {
                    Image(systemName: "heart.fill")
                    Text("压力监测")
                }
                .tag(0)
            
            // 训练页面
            TrainingHubView()
                .tabItem {
                    Image(systemName: "leaf.fill")
                    Text("放松训练")
                }
                .tag(1)
            
            // 数据分析
            AnalyticsView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("数据分析")
                }
                .tag(2)
            
            // 设置
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("设置")
                }
                .tag(3)
        }
        .onAppear {
            setupAppearance()
        }
    }
    
    private func setupAppearance() {
        // 配置全局外观
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        UITabBar.appearance().standardAppearance = appearance
    }
}

// MARK: - 压力可视化主视图
struct StressVisualizationView: View {
    @EnvironmentObject var stressEngine: StressAlgorithmEngine
    @State private var animationPhase: Double = 0
    @State private var showDetails = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景环境
                EnvironmentBackgroundView(stressLevel: stressEngine.currentStressLevel)
                
                VStack(spacing: 30) {
                    // 压力数值显示
                    StressLevelDisplayView(
                        level: stressEngine.currentStressLevel,
                        category: stressEngine.stressCategory,
                        trend: stressEngine.trend
                    )
                    
                    // 主要可视化元素
                    EnergyOrbView(
                        stressLevel: stressEngine.currentStressLevel,
                        animationPhase: animationPhase
                    )
                    .frame(width: geometry.size.width * 0.6, height: geometry.size.width * 0.6)
                    .onTapGesture {
                        withAnimation(.spring()) {
                            showDetails.toggle()
                        }
                    }
                    
                    // 快速操作按钮
                    QuickActionButtonsView()
                    
                    Spacer()
                }
                .padding()
            }
        }
        .onAppear {
            startContinuousAnimation()
        }
        .sheet(isPresented: $showDetails) {
            StressDetailView()
        }
    }
    
    private func startContinuousAnimation() {
        withAnimation(Animation.linear(duration: 2).repeatForever(autoreverses: false)) {
            animationPhase = 1.0
        }
    }
}

// MARK: - 能量球可视化组件
struct EnergyOrbView: View {
    let stressLevel: Double
    let animationPhase: Double
    
    var body: some View {
        ZStack {
            // 主要球体
            Circle()
                .fill(
                    RadialGradient(
                        gradient: stressGradient,
                        center: .center,
                        startRadius: 20,
                        endRadius: 100
                    )
                )
                .scaleEffect(1 + sin(animationPhase * .pi * 2) * 0.1)
            
            // 粒子效果层
            ParticleSystemView(stressLevel: stressLevel, phase: animationPhase)
            
            // 能量波纹
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .stroke(stressColor.opacity(0.3), lineWidth: 2)
                    .scaleEffect(1 + sin(animationPhase * .pi * 2 + Double(index) * 0.5) * 0.2)
            }
        }
    }
    
    private var stressGradient: Gradient {
        switch stressLevel {
        case 0...30:
            return Gradient(colors: [Color.blue.opacity(0.8), Color.green.opacity(0.6)])
        case 31...60:
            return Gradient(colors: [Color.yellow.opacity(0.8), Color.orange.opacity(0.6)])
        default:
            return Gradient(colors: [Color.red.opacity(0.8), Color.purple.opacity(0.6)])
        }
    }
    
    private var stressColor: Color {
        switch stressLevel {
        case 0...30: return .blue
        case 31...60: return .orange
        default: return .red
        }
    }
}
```

### 4. Apple Watch 应用架构

#### 4.1 watchOS 主应用结构

```swift
// MARK: - watchOS 应用入口
@main
struct StressBalanz_watchOS_App: App {
    var body: some Scene {
        WindowGroup {
            WatchContentView()
                .environmentObject(WatchDataManager())
        }
    }
}

// MARK: - Watch 主视图
struct WatchContentView: View {
    @EnvironmentObject var dataManager: WatchDataManager
    @State private var currentPage = 0
    
    var body: some View {
        TabView(selection: $currentPage) {
            // 压力显示页面
            WatchStressView()
                .tag(0)
            
            // 快速训练页面
            WatchTrainingView()
                .tag(1)
            
            // 设置页面
            WatchSettingsView()
                .tag(2)
        }
        .tabViewStyle(PageTabViewStyle())
        .onAppear {
            dataManager.startDataSync()
        }
    }
}

// MARK: - Watch 数据管理器
class WatchDataManager: NSObject, ObservableObject {
    @Published var currentStressLevel: Double = 0
    @Published var syncStatus: SyncStatus = .disconnected
    
    private let session = WCSession.default
    
    enum SyncStatus {
        case connected, disconnected, syncing
    }
    
    override init() {
        super.init()
        setupWatchConnectivity()
    }
    
    private func setupWatchConnectivity() {
        if WCSession.isSupported() {
            session.delegate = self
            session.activate()
        }
    }
    
    func startDataSync() {
        requestStressDataFromPhone()
    }
    
    private func requestStressDataFromPhone() {
        guard session.isReachable else { return }
        
        let message = ["action": "requestStressData"]
        session.sendMessage(message, replyHandler: { [weak self] reply in
            if let stressLevel = reply["stressLevel"] as? Double {
                DispatchQueue.main.async {
                    self?.currentStressLevel = stressLevel
                }
            }
        })
    }
}

// MARK: - WCSessionDelegate 实现
extension WatchDataManager: WCSessionDelegate {
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.syncStatus = activationState == .activated ? .connected : .disconnected
        }
    }
    
    func session(_ session: WCSession, didReceiveMessage message: [String : Any], replyHandler: @escaping ([String : Any]) -> Void) {
        // 处理来自iPhone的数据更新
        if let stressLevel = message["stressLevel"] as? Double {
            DispatchQueue.main.async {
                self.currentStressLevel = stressLevel
            }
        }
    }
}

// MARK: - 表盘复杂功能
struct StressComplicationView: View {
    let stressLevel: Double
    
    var body: some View {
        ZStack {
            Circle()
                .fill(stressColor)
                .frame(width: 20, height: 20)
            
            Text("\(Int(stressLevel))")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.white)
        }
    }
    
    private var stressColor: Color {
        switch stressLevel {
        case 0...30: return .green
        case 31...60: return .yellow
        default: return .red
        }
    }
}
```

---

## 🔐 安全与隐私架构

### 隐私保护策略

```swift
// MARK: - 隐私管理器
class PrivacyManager {
    static let shared = PrivacyManager()
    
    // 数据加密密钥管理
    private let keychain = Keychain(service: "com.stressbalanz.app")
    
    // 敏感数据本地加密
    func encryptSensitiveData(_ data: Data) -> Data? {
        guard let key = getOrCreateEncryptionKey() else { return nil }
        
        // 使用AES-256加密
        return try? AES.GCM.seal(data, using: key).combined
    }
    
    func decryptSensitiveData(_ encryptedData: Data) -> Data? {
        guard let key = getOrCreateEncryptionKey() else { return nil }
        
        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            return try AES.GCM.open(sealedBox, using: key)
        } catch {
            return nil
        }
    }
    
    private func getOrCreateEncryptionKey() -> SymmetricKey? {
        // 从Keychain获取或创建新的加密密钥
        if let keyData = try? keychain.getData("encryption_key") {
            return SymmetricKey(data: keyData)
        } else {
            let newKey = SymmetricKey(size: .bits256)
            let keyData = newKey.withUnsafeBytes { Data($0) }
            try? keychain.set(keyData, key: "encryption_key")
            return newKey
        }
    }
    
    // 数据匿名化处理
    func anonymizeUserData(_ stressData: [StressSession]) -> [AnonymizedStressData] {
        return stressData.map { session in
            AnonymizedStressData(
                timestamp: session.timestamp,
                stressLevel: session.stressLevel,
                category: categorizeStress(session.stressLevel)
            )
        }
    }
    
    private func categorizeStress(_ level: Double) -> String {
        switch level {
        case 0...30: return "low"
        case 31...60: return "moderate"  
        default: return "high"
        }
    }
}

struct AnonymizedStressData {
    let timestamp: Date
    let stressLevel: Double
    let category: String
}
```

---

## 🚀 性能优化策略

### 内存管理优化

```swift
// MARK: - 内存优化管理器
class PerformanceManager {
    static let shared = PerformanceManager()
    
    // 图像缓存管理
    private let imageCache = NSCache<NSString, UIImage>()
    
    // 数据预加载策略
    private var preloadedData: [String: Any] = [:]
    
    init() {
        setupCacheConfiguration()
        observeMemoryWarnings()
    }
    
    private func setupCacheConfiguration() {
        imageCache.countLimit = 50 // 最多缓存50张图片
        imageCache.totalCostLimit = 50 * 1024 * 1024 // 50MB内存限制
    }
    
    private func observeMemoryWarnings() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleMemoryWarning()
        }
    }
    
    private func handleMemoryWarning() {
        // 清理缓存
        imageCache.removeAllObjects()
        preloadedData.removeAll()
        
        // 强制垃圾回收
        DispatchQueue.global(qos: .utility).async {
            // 执行内存清理任务
        }
    }
    
    // 后台任务管理
    func scheduleBackgroundDataProcessing() {
        let identifier = "com.stressbalanz.background-processing"
        
        let request = BGProcessingTaskRequest(identifier: identifier)
        request.requiresNetworkConnectivity = false
        request.requiresExternalPower = false
        
        try? BGTaskScheduler.shared.submit(request)
    }
}

// MARK: - 动画性能优化
struct OptimizedAnimationView: View {
    @State private var animationPhase: Double = 0
    private let animationTimer = Timer.publish(every: 0.016, on: .main, in: .common).autoconnect() // 60 FPS
    
    var body: some View {
        Circle()
            .fill(Color.blue)
            .scaleEffect(1 + sin(animationPhase) * 0.1)
            .onReceive(animationTimer) { _ in
                withAnimation(.linear(duration: 0.016)) {
                    animationPhase += 0.1
                }
            }
            .drawingGroup() // 启用Metal渲染优化
    }
}
```

---

## 📊 监控与分析架构

### 应用性能监控

```swift
// MARK: - 性能监控管理器
class AnalyticsManager {
    static let shared = AnalyticsManager()
    
    private let performanceMetrics = PerformanceMetrics()
    
    // 关键性能指标跟踪
    func trackAppLaunchTime() {
        let launchTime = CFAbsoluteTimeGetCurrent()
        // 记录启动时间
        performanceMetrics.recordLaunchTime(launchTime)
    }
    
    func trackMemoryUsage() {
        let memoryInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let result = withUnsafeMutablePointer(to: &memoryInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if result == KERN_SUCCESS {
            let memoryUsage = Double(memoryInfo.resident_size) / (1024 * 1024) // MB
            performanceMetrics.recordMemoryUsage(memoryUsage)
        }
    }
    
    // 用户行为分析
    func trackUserAction(_ action: UserAction) {
        let event = AnalyticsEvent(
            action: action,
            timestamp: Date(),
            metadata: gatherContextualData()
        )
        
        // 本地存储，定期聚合分析
        persistEvent(event)
    }
    
    private func gatherContextualData() -> [String: Any] {
        return [
            "device_model": UIDevice.current.model,
            "ios_version": UIDevice.current.systemVersion,
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
        ]
    }
}

enum UserAction: String {
    case launchApp = "app_launch"
    case startTraining = "training_start"
    case completeTraining = "training_complete"
    case viewStressData = "stress_data_view"
    case shareData = "data_share"
}

struct AnalyticsEvent {
    let action: UserAction
    let timestamp: Date
    let metadata: [String: Any]
}
```

---

## 🧪 测试架构设计

### 单元测试框架

```swift
// MARK: - 压力算法测试
import XCTest
@testable import StressBalanz

class StressAlgorithmTests: XCTestCase {
    
    var algorithmEngine: StressAlgorithmEngine!
    
    override func setUp() {
        super.setUp()
        algorithmEngine = StressAlgorithmEngine()
    }
    
    override func tearDown() {
        algorithmEngine = nil
        super.tearDown()
    }
    
    func testStressCalculationWithNormalHRV() {
        // Given
        let hrv = 35.0
        let heartRate = 65.0
        let sleepQuality = 0.8
        
        // When
        let stressLevel = algorithmEngine.calculateStressLevel(
            hrv: hrv,
            heartRate: heartRate,
            sleepQuality: sleepQuality
        )
        
        // Then
        XCTAssertGreaterThanOrEqual(stressLevel, 0)
        XCTAssertLessThanOrEqual(stressLevel, 100)
        XCTAssertLessThan(stressLevel, 50, "正常HRV应该对应较低压力值")
    }
    
    func testStressCalculationWithLowHRV() {
        // Given
        let hrv = 15.0 // 较低的HRV值
        let heartRate = 85.0 // 较高的心率
        let sleepQuality = 0.4 // 较差的睡眠质量
        
        // When
        let stressLevel = algorithmEngine.calculateStressLevel(
            hrv: hrv,
            heartRate: heartRate,
            sleepQuality: sleepQuality
        )
        
        // Then
        XCTAssertGreaterThan(stressLevel, 60, "低HRV应该对应较高压力值")
    }
    
    func testStressCategoryClassification() {
        // Test low stress
        XCTAssertEqual(algorithmEngine.categorizeStress(25), .low)
        
        // Test moderate stress  
        XCTAssertEqual(algorithmEngine.categorizeStress(45), .moderate)
        
        // Test high stress
        XCTAssertEqual(algorithmEngine.categorizeStress(75), .high)
    }
}

// MARK: - UI测试
class StressBalanzUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUp() {
        super.setUp()
        continueAfterFailure = false
        app = XCUIApplication()
        app.launch()
    }
    
    func testMainViewElements() {
        // 验证主要UI元素存在
        XCTAssertTrue(app.tabBars["主标签栏"].exists)
        XCTAssertTrue(app.buttons["压力监测"].exists)
        XCTAssertTrue(app.buttons["放松训练"].exists)
        XCTAssertTrue(app.buttons["数据分析"].exists)
    }
    
    func testTrainingFlowNavigation() {
        // 测试训练流程导航
        app.tabBars.buttons["放松训练"].tap()
        
        XCTAssertTrue(app.buttons["开始呼吸训练"].exists)
        
        app.buttons["开始呼吸训练"].tap()
        
        // 验证训练界面元素
        XCTAssertTrue(app.staticTexts["深呼吸4秒"].exists)
    }
}
```

---

## 🔧 配置与部署

### 构建配置管理

```swift
// MARK: - 配置管理
struct AppConfiguration {
    static let shared = AppConfiguration()
    
    // 环境配置
    enum Environment {
        case development
        case staging
        case production
        
        var baseURL: String {
            switch self {
            case .development: return "https://dev-api.stressbalanz.com"
            case .staging: return "https://staging-api.stressbalanz.com"
            case .production: return "https://api.stressbalanz.com"
            }
        }
        
        var isAnalyticsEnabled: Bool {
            switch self {
            case .development: return false
            case .staging: return true
            case .production: return true
            }
        }
    }
    
    // 从Info.plist读取当前环境
    var currentEnvironment: Environment {
        guard let envString = Bundle.main.infoDictionary?["ENVIRONMENT"] as? String else {
            return .development
        }
        
        switch envString {
        case "STAGING": return .staging
        case "PRODUCTION": return .production
        default: return .development
        }
    }
    
    // 算法参数配置
    var algorithmParameters: AlgorithmParameters {
        AlgorithmParameters(
            hrvWeight: 0.7,
            heartRateWeight: 0.2,
            sleepWeight: 0.1,
            smoothingFactor: 0.3
        )
    }
}

struct AlgorithmParameters {
    let hrvWeight: Double
    let heartRateWeight: Double
    let sleepWeight: Double
    let smoothingFactor: Double
}
```

## 📝 总结与最佳实践

### 关键设计决策

1. **架构模式选择**: 采用MVVM+Combine的响应式架构，确保数据流的清晰和可维护性
2. **数据本地化**: 所有健康数据处理都在设备本地完成，保证用户隐私安全
3. **性能优化**: 通过缓存、后台处理和内存管理优化确保应用流畅运行
4. **可扩展性**: 模块化设计允许未来轻松添加新功能和算法改进

### 开发建议

1. **代码质量**: 严格遵循Swift编程规范，保持高代码覆盖率
2. **用户体验**: 注重细节动画和交互反馈，创造愉悦的使用体验  
3. **测试策略**: 实施全面的测试策略，包括单元测试、集成测试和UI测试
4. **持续改进**: 基于用户反馈和数据分析持续优化算法和功能

### 风险控制

1. **技术风险**: 建立备用方案，避免单一技术依赖
2. **隐私风险**: 严格遵循隐私设计原则，定期进行安全审查  
3. **性能风险**: 建立性能监控体系，及时发现和解决性能问题
4. **兼容性风险**: 确保在不同设备和系统版本上的兼容性

这个技术架构设计为StressBalanz项目提供了坚实的技术基础，支持项目的长期发展和持续创新。