import Foundation
import Combine
import SwiftUI
import HealthKit

/**
 StressAnalysisService
 
 压力分析服务 - 集成所有分析算法到主应用
 协调HealthKit数据、压力分析引擎、多指标融合、个人基线校准
 */
class StressAnalysisService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentStressLevel: Double = 0.0
    @Published var stressClassification: StressAnalysisEngine.StressLevel = .moderate
    @Published var analysisConfidence: Double = 0.0
    @Published var isAnalyzing: Bool = false
    @Published var lastAnalysisTime: Date?
    @Published var personalBaseline: PersonalBaselineCalibrationEngine.PersonalBaseline?
    @Published var analysisHistory: [AnalysisRecord] = []
    @Published var recommendations: [String] = []
    
    // MARK: - Core Components
    
    private let stressAnalysisEngine = StressAnalysisEngine()
    private let fusionEngine = MultiMetricFusionEngine()
    let baselineEngine: PersonalBaselineCalibrationEngine
    private let healthKitManager: HealthKitManager
    private let dataValidationService = DataValidationService()
    
    // MARK: - Configuration
    
    private struct ServiceConfig {
        static let analysisInterval: TimeInterval = 15 * 60 // 15分钟
        static let batchAnalysisInterval: TimeInterval = 2 * 60 * 60 // 2小时
        static let maxHistorySize: Int = 100
        static let minConfidenceThreshold: Double = 0.6
        static let emergencyStressThreshold: Double = 0.9
    }
    
    // MARK: - Data Models
    
    struct AnalysisRecord {
        let id = UUID()
        let timestamp: Date
        let stressLevel: Double
        let classification: StressAnalysisEngine.StressLevel
        let confidence: Double
        let fusionResult: MultiMetricFusionEngine.FusionResult?
        let dataQuality: DataValidationService.DataQuality
        let recommendations: [String]
        let contributingFactors: [StressAnalysisEngine.StressFactor]
        
        var isReliable: Bool {
            return confidence >= ServiceConfig.minConfidenceThreshold &&
                   dataQuality != .invalid
        }
        
        var isEmergency: Bool {
            return stressLevel >= ServiceConfig.emergencyStressThreshold && isReliable
        }
    }
    
    // MARK: - Cancellables
    
    var cancellables = Set<AnyCancellable>()
    private var analysisTimer: Timer?
    
    // MARK: - Initialization
    
    init(healthKitManager: HealthKitManager, persistenceController: PersistenceController) {
        self.healthKitManager = healthKitManager
        self.baselineEngine = PersonalBaselineCalibrationEngine(
            context: persistenceController.container.viewContext
        )
        
        setupNotificationObservers()
        setupAnalysisTimer()
        
        // 启动时加载个人基线
        Task {
            await loadPersonalBaseline()
        }
    }
    
    deinit {
        analysisTimer?.invalidate()
    }
    
    // MARK: - Public Interface
    
    func startContinuousAnalysis() {
        guard healthKitManager.isAuthorized else {
            print("HealthKit未授权，无法开始连续分析")
            return
        }
        
        print("开始连续压力分析...")
        analysisTimer?.invalidate()
        
        analysisTimer = Timer.scheduledTimer(withTimeInterval: ServiceConfig.analysisInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.performAutomaticAnalysis()
            }
        }
        
        // 立即执行一次分析
        Task {
            await performAutomaticAnalysis()
        }
    }
    
    func stopContinuousAnalysis() {
        print("停止连续压力分析")
        analysisTimer?.invalidate()
        analysisTimer = nil
    }
    
    func performManualAnalysis() async {
        await performStressAnalysis(isManual: true)
    }
    
    func refreshPersonalBaseline() async {
        guard let userProfile = await getCurrentUserProfile() else {
            print("无法获取用户配置文件")
            return
        }
        
        await MainActor.run {
            isAnalyzing = true
        }
        
        let updatedBaseline = await baselineEngine.updateBaseline(for: userProfile.id!)
        
        await MainActor.run {
            personalBaseline = updatedBaseline
            isAnalyzing = false
        }
        
        print("个人基线已更新")
    }
    
    func getStressInsights() -> StressInsights {
        let recentAnalyses = analysisHistory.prefix(10)
        let avgStress = recentAnalyses.map { $0.stressLevel }.reduce(0, +) / Double(recentAnalyses.count)
        let trend = calculateStressTrend(from: Array(recentAnalyses))
        
        return StressInsights(
            currentLevel: currentStressLevel,
            averageLevel: avgStress,
            trend: trend,
            confidence: analysisConfidence,
            dataQuality: getOverallDataQuality(),
            recommendations: recommendations,
            lastUpdate: lastAnalysisTime ?? Date()
        )
    }
    
    // MARK: - Private Analysis Methods
    
    func performAutomaticAnalysis() async {
        guard !isAnalyzing else { return }
        
        await performStressAnalysis(isManual: false)
    }
    
    private func performStressAnalysis(isManual: Bool) async {
        await MainActor.run {
            isAnalyzing = true
        }
        
        do {
            // 1. 获取最新健康数据
            let healthData = await collectLatestHealthData()
            
            // 2. 验证数据质量
            let dataValidation = validateHealthData(healthData)
            
            guard dataValidation.qualityLevel != .invalid else {
                print("数据质量不足，跳过分析")
                await MainActor.run {
                    isAnalyzing = false
                }
                return
            }
            
            // 3. 执行压力分析
            let analysisResult = await executeStressAnalysis(healthData)
            
            // 4. 多指标融合
            let fusionResult = await performMultiMetricFusion(
                healthData: healthData,
                analysisResult: analysisResult
            )
            
            // 5. 创建分析记录
            let recommendations = generateRecommendations(for: fusionResult.fusedStressLevel, classification: classifyStressLevel(fusionResult.fusedStressLevel))
            
            let record = AnalysisRecord(
                timestamp: Date(),
                stressLevel: fusionResult.fusedStressLevel,
                classification: classifyStressLevel(fusionResult.fusedStressLevel),
                confidence: fusionResult.confidence,
                fusionResult: fusionResult,
                dataQuality: dataValidation.qualityLevel,
                recommendations: recommendations,
                contributingFactors: analysisResult?.contributingFactors ?? []
            )
            
            // 6. 更新UI和历史记录
            await updateAnalysisResults(record)
            
            // 7. 检查紧急情况
            if record.isEmergency {
                await handleEmergencyStressLevel(record)
            }
            
        } catch {
            print("压力分析失败: \(error)")
        }
        
        await MainActor.run {
            isAnalyzing = false
        }
    }
    
    private func collectLatestHealthData() async -> LatestHealthData {
        let endTime = Date()
        let startTime = Calendar.current.date(byAdding: .hour, value: -2, to: endTime) ?? endTime
        
        async let hrvData = healthKitManager.fetchHRVData(from: startTime, to: endTime)
        async let heartRateData = healthKitManager.fetchHeartRateData(from: startTime, to: endTime)
        async let sleepData = healthKitManager.fetchLastNightSleep()
        
        let hrv = await hrvData
        let heartRate = await heartRateData
        let sleep = await sleepData
        
        return LatestHealthData(
            hrvSamples: hrv,
            heartRateSamples: heartRate,
            sleepQuality: sleep,
            timestamp: endTime
        )
    }
    
    private func validateHealthData(_ data: LatestHealthData) -> ValidationReport {
        var validationResults: [DataValidationService.ValidationResult] = []
        
        // 验证HRV数据
        if !data.hrvSamples.isEmpty {
            let hrvValidation = dataValidationService.validateHRVData(data.hrvSamples)
            validationResults.append(hrvValidation)
        }
        
        // 验证心率数据
        if !data.heartRateSamples.isEmpty {
            let heartRateValidation = dataValidationService.validateHeartRateData(data.heartRateSamples)
            validationResults.append(heartRateValidation)
        }
        
        // 验证睡眠数据
        if let sleepData = data.sleepQuality {
            let sleepValidation = dataValidationService.validateSleepData(sleepData)
            validationResults.append(sleepValidation)
        }
        
        return dataValidationService.generateValidationReport(for: validationResults)
    }
    
    private func executeStressAnalysis(_ data: LatestHealthData) async -> StressAnalysisEngine.StressAnalysisResult? {
        // 准备数据
        let hrvValues = data.hrvSamples.map { $0.quantity.doubleValue(for: HKUnit.secondUnit(with: .milli)) }
        let heartRateValues = data.heartRateSamples.map { $0.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute())) }
        
        // 获取用户基线
        let userBaseline = personalBaseline.map { baseline in
            UserBaseline(
                baselineHRV: baseline.hrvBaseline.meanRMSSD,
                baselineHeartRate: baseline.heartRateBaseline.restingHeartRate,
                establishedDate: baseline.establishedDate,
                confidence: baseline.confidence,
                sampleSize: baseline.sampleSize
            )
        }
        
        // 创建分析上下文
        let context = AnalysisContext(
            timestamp: data.timestamp,
            userActivity: nil,
            environmentalFactors: nil
        )
        
        // 执行分析
        return stressAnalysisEngine.analyzeStress(
            hrvData: hrvValues,
            heartRateData: heartRateValues,
            sleepQuality: data.sleepQuality,
            userBaseline: userBaseline,
            context: context
        )
    }
    
    private func performMultiMetricFusion(
        healthData: LatestHealthData,
        analysisResult: StressAnalysisEngine.StressAnalysisResult?
    ) async -> MultiMetricFusionEngine.FusionResult {
        
        // 准备融合数据
        let heartRateAnalysis = createHeartRateAnalysis(from: healthData.heartRateSamples)
        let sleepAnalysis = createSleepAnalysis(from: healthData.sleepQuality)
        let contextAnalysis = createContextAnalysis(timestamp: healthData.timestamp)
        
        // 获取历史数据用于融合
        let historicalData: [StressAnalysisEngine.StressAnalysisResult] = analysisHistory.prefix(10).compactMap { record -> StressAnalysisEngine.StressAnalysisResult? in
            guard let result = record.fusionResult else { return nil }
            
            // 简化转换为StressAnalysisResult
            return StressAnalysisEngine.StressAnalysisResult(
                stressLevel: result.fusedStressLevel,
                classification: record.classification,
                confidence: result.confidence,
                contributingFactors: record.contributingFactors,
                recommendations: record.recommendations,
                timestamp: record.timestamp,
                analysisMetadata: StressAnalysisEngine.AnalysisMetadata(
                    dataPoints: 0,
                    hrvMetrics: nil,
                    heartRateMetrics: nil,
                    processingTime: 0
                )
            )
        }
        
        // 执行多指标融合
        return fusionEngine.fuseMetrics(
            hrvAnalysis: analysisResult,
            heartRateAnalysis: heartRateAnalysis,
            sleepAnalysis: sleepAnalysis,
            contextAnalysis: contextAnalysis,
            historicalData: historicalData,
            method: .adaptiveWeighting
        )
    }
    
    private func updateAnalysisResults(_ record: AnalysisRecord) async {
        await MainActor.run {
            // 更新当前状态
            currentStressLevel = record.stressLevel
            stressClassification = record.classification
            analysisConfidence = record.confidence
            lastAnalysisTime = record.timestamp
            recommendations = record.recommendations
            
            // 添加到历史记录
            analysisHistory.insert(record, at: 0)
            
            // 保持历史记录在限制范围内
            if analysisHistory.count > ServiceConfig.maxHistorySize {
                analysisHistory = Array(analysisHistory.prefix(ServiceConfig.maxHistorySize))
            }
        }
        
        // 持久化分析结果
        await saveAnalysisRecord(record)
        
        // 发送通知
        NotificationCenter.default.post(
            name: .stressAnalysisCompleted,
            object: record
        )
    }
    
    // MARK: - Helper Methods
    
    private func generateRecommendations(for stressLevel: Double, classification: StressAnalysisEngine.StressLevel) -> [String] {
        var recommendations: [String] = []
        
        switch classification {
        case .veryLow, .low:
            recommendations.append("压力水平较低，继续保持良好状态")
        case .moderate:
            recommendations.append("进行5分钟深呼吸练习")
            recommendations.append("适当休息，避免过度劳累")
        case .high:
            recommendations.append("立即进行减压活动")
            recommendations.append("考虑进行正念冥想")
        case .veryHigh:
            recommendations.append("压力水平过高，请立即休息")
            recommendations.append("建议寻求专业帮助")
        }
        
        return recommendations
    }
    
    private func createHeartRateAnalysis(from samples: [HKQuantitySample]) -> HeartRateAnalysis? {
        guard !samples.isEmpty else { return nil }
        
        let values = samples.map { $0.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute())) }
        let avgHeartRate = values.reduce(0, +) / Double(values.count)
        
        // 基于个人基线计算压力水平
        let baselineHR = personalBaseline?.heartRateBaseline.restingHeartRate ?? 70.0
        let stressLevel = min(1.0, max(0.0, (avgHeartRate - baselineHR) / 30.0))
        
        return HeartRateAnalysis(
            stressLevel: stressLevel,
            confidence: 0.8,
            isValid: true
        )
    }
    
    private func createSleepAnalysis(from sleepQuality: SleepQualityData?) -> SleepAnalysis? {
        guard let sleep = sleepQuality else { return nil }
        
        let stressImpact = 1.0 - (sleep.qualityScore / 100.0)
        
        return SleepAnalysis(
            stressImpact: stressImpact,
            confidence: 0.7,
            isValid: true
        )
    }
    
    private func createContextAnalysis(timestamp: Date) -> ContextAnalysis {
        let hour = Calendar.current.component(.hour, from: timestamp)
        
        // 基于时间的压力修饰因子
        var stressModifier: Double = 0.5
        
        switch hour {
        case 9...11: stressModifier = 0.6 // 上午工作时间
        case 14...16: stressModifier = 0.7 // 下午疲劳时段
        case 18...20: stressModifier = 0.4 // 下班时间
        case 22...6: stressModifier = 0.3 // 休息时间
        default: stressModifier = 0.5
        }
        
        return ContextAnalysis(
            stressModifier: stressModifier,
            confidence: 0.6,
            isValid: true
        )
    }
    
    private func classifyStressLevel(_ level: Double) -> StressAnalysisEngine.StressLevel {
        for stressLevel in StressAnalysisEngine.StressLevel.allCases.reversed() {
            if stressLevel.range.contains(level) {
                return stressLevel
            }
        }
        return .moderate
    }
    
    func calculateStressTrend(from records: [AnalysisRecord]) -> StressTrend {
        guard records.count >= 3 else { return .stable }
        
        let recentLevels = records.prefix(3).map { $0.stressLevel }
        let olderLevels = records.dropFirst(3).prefix(3).map { $0.stressLevel }
        
        guard !olderLevels.isEmpty else { return .stable }
        
        let recentAvg = recentLevels.reduce(0, +) / Double(recentLevels.count)
        let olderAvg = olderLevels.reduce(0, +) / Double(olderLevels.count)
        
        let difference = recentAvg - olderAvg
        
        if difference > 0.1 {
            return .increasing
        } else if difference < -0.1 {
            return .decreasing
        } else {
            return .stable
        }
    }
    
    private func getOverallDataQuality() -> DataValidationService.DataQuality {
        guard let lastAnalysis = analysisHistory.first else { return .invalid }
        return lastAnalysis.dataQuality
    }
    
    // MARK: - Data Persistence
    
    private func saveAnalysisRecord(_ record: AnalysisRecord) async {
        // 保存到Core Data
        await MainActor.run {
            let context = baselineEngine.managedObjectContext
            
            let stressReading = StressReading(context: context)
            stressReading.timestamp = record.timestamp
            stressReading.stressLevel = record.stressLevel
            stressReading.confidence = record.confidence
            stressReading.dataSource = "StressAnalysisService"
            
            // 如果有HRV和心率数据，也保存
            if let fusionResult = record.fusionResult {
                if let hrvContribution = fusionResult.individualContributions["HRV"] {
                    stressReading.hrv = hrvContribution * 50.0 // 转换为大致的HRV值
                }
                if let hrContribution = fusionResult.individualContributions["HeartRate"] {
                    stressReading.heartRate = hrContribution * 40.0 + 60.0 // 转换为大致的心率值
                }
            }
            
            do {
                try context.save()
            } catch {
                print("保存分析记录失败: \(error)")
            }
        }
    }
}

// MARK: - Supporting Structures

struct LatestHealthData {
    let hrvSamples: [HKQuantitySample]
    let heartRateSamples: [HKQuantitySample]
    let sleepQuality: SleepQualityData?
    let timestamp: Date
}

struct StressInsights {
    let currentLevel: Double
    let averageLevel: Double
    let trend: StressTrend
    let confidence: Double
    let dataQuality: DataValidationService.DataQuality
    let recommendations: [String]
    let lastUpdate: Date
}

enum StressTrend {
    case increasing
    case stable
    case decreasing
    
    var description: String {
        switch self {
        case .increasing: return "上升"
        case .stable: return "稳定"
        case .decreasing: return "下降"
        }
    }
    
    var color: Color {
        switch self {
        case .increasing: return .red
        case .stable: return .yellow
        case .decreasing: return .green
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let stressAnalysisCompleted = Notification.Name("stressAnalysisCompleted")
    static let emergencyStressDetected = Notification.Name("emergencyStressDetected")
    static let personalBaselineUpdated = Notification.Name("personalBaselineUpdated")
}