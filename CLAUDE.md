# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**StressBalanz** is an iOS stress management and mental health companion app built with SwiftUI. The app uses Apple Watch integration to monitor stress levels through HealthKit data and provides science-based interventions for stress management.

## Development Environment

- **Platform**: iOS 18.5+, watchOS 10+ (planned)
- **Language**: Swift 5.0
- **UI Framework**: SwiftUI
- **Data**: Core Data for local persistence, HealthKit for health data
- **IDE**: Xcode 16.4+
- **Devices**: iPhone and iPad (TARGETED_DEVICE_FAMILY = "1,2")

## Build Commands

```bash
# Build the project
xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Debug build

# Build for device
xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz -configuration Release -destination generic/platform=iOS build

# Clean build folder
xcodebuild -project StressBalanz.xcodeproj -scheme StressBalanz clean
```

## Architecture

### Current State
- **Entry Point**: `StressBalanzApp.swift` - SwiftUI App with Core Data integration
- **Main View**: `ContentView.swift` - Currently contains template CRUD interface
- **Data Layer**: `Persistence.swift` - Core Data stack configuration
- **Data Model**: `StressBalanz.xcdatamodeld` - Simple Item entity (placeholder)

### Planned Architecture (from PRD.md)
The app will be structured around these core modules:

1. **HealthKit Integration Module** - HRV, heart rate monitoring
2. **Stress Algorithm Engine** - Scientific stress level calculation  
3. **Visualization Engine** - Dynamic "energy ball" stress representation
4. **Training Modules** - Breathing exercises, mindfulness, body scanning
5. **Analytics Dashboard** - Long-term trend analysis
6. **Apple Watch Companion** - watchOS app for real-time monitoring

### Key Implementation Notes

- **Privacy First**: All health data processing must remain local-only (no server uploads)
- **Core Data**: Current model needs expansion for stress monitoring data
- **HealthKit**: Requires authorization for HRV, resting heart rate, sleep analysis, standing heart rate, workout data
- **No External Dependencies**: Project currently has no Swift Package Manager or CocoaPods dependencies

## Development Workflow

### Testing
Currently no testing framework is configured. When adding tests:
- Use XCTest for unit tests
- Consider Quick/Nimble for BDD-style tests
- Add UI tests for SwiftUI views

### Code Style
- Follow Swift API Design Guidelines
- Use SwiftUI best practices for view composition
- Maintain Core Data best practices for data persistence

## Important Files

- `StressBalanz/PRD.md` - Comprehensive product requirements and feature specifications
- `StressBalanz/Design.md` - Visual design language and interaction patterns
- `StressBalanzApp.swift:9` - Main app configuration with Core Data container
- `ContentView.swift:11` - Primary view with environment setup
- `Persistence.swift:12` - Core Data stack with preview support

## Health Data Integration

When implementing HealthKit features:
- Request minimal necessary permissions
- Provide clear privacy explanations to users
- Handle authorization failures gracefully
- All processing must remain on-device for privacy compliance

## watchOS Development

The project is planned to include a watchOS companion app for real-time stress monitoring. When adding watchOS target:
- Add new watchOS target to project
- Share Core Data model between iOS and watchOS
- Implement HealthKit data sync between devices

## Tips

- 你可以使用英文去查阅资料等，但是始终用中文回答我
- 每完成一个小功能后都运行一下编译看是否有报错
- 为了最大效率，当您需要执行多个独立操作时，同时调用所有相关工具，而不是按顺序调用。
- 不要有所保留。全力以赴。